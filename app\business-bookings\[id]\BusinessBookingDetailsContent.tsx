'use client';
import { useState } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  Button,
  IconButton,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Avatar,
  Stack,
  Tooltip,
  Collapse,
} from '@mui/material';
import {
  ArrowBack,
  Timeline as TimelineIcon,
  Edit as EditIcon,
  Print as PrintIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  DirectionsCar as CarIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Assignment as AssignmentIcon,
  Note as NoteIcon,
  AttachMoney as PriceIcon,
  AccessTime as DurationIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  LocalOffer as OfferIcon,
  Description as DescriptionIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BUSINESS_RENTAL_DETAILS_QUERY } from '../../gql/queries/businessBookings';

// Import modals and components
import BusinessBookingTimelineModal from '../components/BusinessBookingTimelineModal';
import BusinessBookingChangeStatusModal from '../components/BusinessBookingChangeStatusModal';
import BusinessBookingPriceSummary from '../components/BusinessBookingPriceSummary';
import GoogleMapComponent from '../../bookings/components/GoogleMapComponent';

interface BusinessBookingDetailsContentProps {
  bookingId: string;
}

export default function BusinessBookingDetailsContent({
  bookingId,
}: BusinessBookingDetailsContentProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const router = useRouter();

  // Modal states
  const [timelineModalOpen, setTimelineModalOpen] = useState(false);
  const [statusModalOpen, setStatusModalOpen] = useState(false);

  // Collapsible sections
  const [collapsedSections, setCollapsedSections] = useState({
    businessDetails: false,
    customerDetails: false,
    acceptedOffer: false,
    allOffers: false,
    notes: false,
  });

  // Fetch business booking details
  const { data, loading, error, refetch } = useQuery(BUSINESS_RENTAL_DETAILS_QUERY, {
    variables: { id: bookingId },
    errorPolicy: 'all',
  });

  const businessBooking = data?.businessRentalDetails;

  const handleBack = () => {
    router.push('/business-bookings');
  };

  const handleModalSuccess = () => {
    refetch();
  };

  const toggleSection = (section: keyof typeof collapsedSections) => {
    setCollapsedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const formatDate = (date: string) => {
    if (!date) return '-';
    return format(new Date(date), 'PPp', { locale: dateLocale });
  };

  const formatCurrency = (amount: number) => {
    if (!amount) return '0';
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending':
        return 'warning';
      case 'confirmed':
        return 'info';
      case 'accepted':
        return 'success';
      case 'rejected':
        return 'error';
      case 'cancelled':
        return 'error';
      case 'closed':
        return 'success';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !businessBooking) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('Error loading business booking details. Please try again later.')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 }, direction: isRTL ? 'rtl' : 'ltr' }}>
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
        }}
      >
        <Grid container spacing={2} sx={{ display: 'flex', alignItems: 'center' }}>
          <Grid item xs={12} sm={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton
                onClick={handleBack}
                sx={{
                  mr: isRTL ? 0 : 1,
                  ml: isRTL ? 1 : 0,
                  transform: isRTL ? 'scaleX(-1)' : 'none',
                }}
              >
                <ArrowBack />
              </IconButton>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
                  {t('businessBookings.details.title')}
                </Typography>
                <Typography variant="subtitle1" color="text.secondary">
                  {t('businessBookings.details.bookingNumber')}: {businessBooking.bookingNo}
                </Typography>
              </Box>
            </Box>
          </Grid>

          <Grid item xs={12} sm={12}>
            <Box
              sx={{
                display: 'flex',
                gap: { xs: 1, sm: 2 },
                flexWrap: 'wrap',
                alignItems: 'center',
                justifyContent: { xs: 'center', sm: 'flex-end' },
              }}
            >
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#1976d2',
                  '&:hover': { backgroundColor: '#115293' },
                  minWidth: { xs: '100px', sm: '120px' },
                  height: '36px',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                }}
                startIcon={<TimelineIcon />}
                onClick={() => setTimelineModalOpen(true)}
                size="small"
              >
                {t('businessBookings.actions.timeline')}
              </Button>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: '#9c27b0',
                  '&:hover': { backgroundColor: '#7b1fa2' },
                  minWidth: { xs: '120px', sm: '140px' },
                  height: '36px',
                  fontSize: { xs: '0.75rem', sm: '0.875rem' },
                }}
                startIcon={<EditIcon />}
                onClick={() => setStatusModalOpen(true)}
                size="small"
              >
                {t('businessBookings.actions.changeStatus')}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Two Column Layout - Responsive */}
      <Grid container spacing={3}>
        {/* Left Column - Full width on mobile, half on desktop */}
        <Grid item xs={12} lg={6}>
          {/* Customer Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <PersonIcon color="primary" />
                <Typography variant="h6">
                  {t('businessBookings.details.customerInformation')}
                </Typography>
                <IconButton size="small" onClick={() => toggleSection('customerDetails')}>
                  {collapsedSections.customerDetails ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </IconButton>
              </Box>
              <Collapse in={!collapsedSections.customerDetails}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <Avatar sx={{ width: 60, height: 60 }}>
                    {businessBooking.customerName?.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{businessBooking.customerName}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('businessBookings.details.customerId')}: {businessBooking.userId}
                    </Typography>
                  </Box>
                </Box>
                <Stack spacing={1}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PhoneIcon fontSize="small" color="action" />
                    <Typography variant="body2">{businessBooking.phone}</Typography>
                  </Box>
                  {businessBooking.companyEmail && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <EmailIcon fontSize="small" color="action" />
                      <Typography variant="body2">{businessBooking.companyEmail}</Typography>
                    </Box>
                  )}
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BusinessIcon fontSize="small" color="action" />
                    <Typography variant="body2">{businessBooking.companyName}</Typography>
                  </Box>
                </Stack>
              </Collapse>
            </CardContent>
          </Card>

          {/* Business Booking Details */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <BusinessIcon color="primary" />
                <Typography variant="h6">
                  {t('businessBookings.details.businessDetails')}
                </Typography>
                <IconButton size="small" onClick={() => toggleSection('businessDetails')}>
                  {collapsedSections.businessDetails ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </IconButton>
              </Box>
              <Collapse in={!collapsedSections.businessDetails}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Typography variant="h6">{t('Status')}</Typography>
                      <Chip
                        label={t(businessBooking.statusLocalized || businessBooking.status)}
                        color={getStatusColor(businessBooking.status) as any}
                        variant="filled"
                      />
                    </Box>
                    <Stack spacing={1}>
                      <Typography variant="body2">
                        {t('businessBookings.details.businessActivity')}:{' '}
                        {isRTL
                          ? businessBooking.arBusinessActivity
                          : businessBooking.enBusinessActivity}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.carDetails')}:{' '}
                        {isRTL ? businessBooking.arMakeName : businessBooking.enMakeName}{' '}
                        {isRTL ? businessBooking.arModelName : businessBooking.enModelName}{' '}
                        {businessBooking.year}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.numberOfCars')}: {businessBooking.numberOfCars}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.numberOfMonths')}:{' '}
                        {businessBooking.numberOfMonths}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.pickupLocation')}:{' '}
                        {isRTL
                          ? businessBooking.arPickUpCityName
                          : businessBooking.enPickUpCityName}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.pickupDate')}:{' '}
                        {formatDate(businessBooking.pickUpDatetime)}
                      </Typography>
                      {businessBooking.dropOffDatetime && (
                        <Typography variant="body2">
                          {t('businessBookings.details.dropOffDate')}:{' '}
                          {formatDate(businessBooking.dropOffDatetime)}
                        </Typography>
                      )}
                      <Typography variant="body2">
                        {t('businessBookings.details.insurance')}: {businessBooking.insuranceName}
                      </Typography>
                      {businessBooking.pricePerMonth && (
                        <Typography variant="body2">
                          {t('businessBookings.details.pricePerMonth')}:{' '}
                          {formatCurrency(businessBooking.pricePerMonth)}
                        </Typography>
                      )}
                      {businessBooking.totalBookingPrice && (
                        <Typography variant="body2">
                          {t('businessBookings.details.totalPrice')}:{' '}
                          {formatCurrency(businessBooking.totalBookingPrice)}
                        </Typography>
                      )}
                      <Typography variant="body2" color="text.secondary">
                        {t('Created')}: {formatDate(businessBooking.createdAt)}
                      </Typography>
                      {businessBooking.cancelledAt && (
                        <Typography variant="body2" color="error">
                          {t('Cancelled')}: {formatDate(businessBooking.cancelledAt)}
                        </Typography>
                      )}
                      {businessBooking.closedAt && (
                        <Typography variant="body2" color="success.main">
                          {t('Closed')}: {formatDate(businessBooking.closedAt)}
                        </Typography>
                      )}
                    </Stack>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Price Summary */}
          <BusinessBookingPriceSummary businessBooking={businessBooking} />
        </Grid>

        {/* Right Column - Full width on mobile, half on desktop */}
        <Grid item xs={12} lg={6}>
          {/* Accepted Offer Details */}
          {businessBooking.acceptedOffer && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <OfferIcon color="primary" />
                  <Typography variant="h6">
                    {t('businessBookings.details.acceptedOffer')}
                  </Typography>
                  <IconButton size="small" onClick={() => toggleSection('acceptedOffer')}>
                    {collapsedSections.acceptedOffer ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                  </IconButton>
                </Box>
                <Collapse in={!collapsedSections.acceptedOffer}>
                  <Stack spacing={2}>
                    <Typography variant="body2">
                      {t('businessBookings.details.offerPrice')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.offerPrice)}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.allyCompany')}:{' '}
                      {businessBooking.acceptedOffer.allyCompanyName}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.kilometerPerMonth')}:{' '}
                      {businessBooking.acceptedOffer.kilometerPerMonth}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.additionalKilometer')}:{' '}
                      {businessBooking.acceptedOffer.additionalKilometer}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.carInsuranceStandard')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.carInsuranceStandard)}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.carInsuranceFull')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.carInsuranceFull)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('businessBookings.details.offerDate')}:{' '}
                      {formatDate(businessBooking.acceptedOffer.createdAt)}
                    </Typography>
                  </Stack>
                </Collapse>
              </CardContent>
            </Card>
          )}

          {/* All Offers */}
          {businessBooking.businessRentalOffers &&
            businessBooking.businessRentalOffers.length > 0 && (
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <OfferIcon color="secondary" />
                    <Typography variant="h6">
                      {t('businessBookings.details.allOffers')} (
                      {businessBooking.businessRentalOffers.length})
                    </Typography>
                    <IconButton size="small" onClick={() => toggleSection('allOffers')}>
                      {collapsedSections.allOffers ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                    </IconButton>
                  </Box>
                  <Collapse in={!collapsedSections.allOffers}>
                    <Stack spacing={2}>
                      {businessBooking.businessRentalOffers.map((offer: any, index: number) => (
                        <Paper key={offer.id} sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            {t('businessBookings.details.offer')} #{index + 1} -{' '}
                            {offer.allyCompanyName}
                          </Typography>
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.offerPrice')}:{' '}
                                {formatCurrency(offer.offerPrice)}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.kilometerPerMonth')}:{' '}
                                {offer.kilometerPerMonth}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.additionalKilometer')}:{' '}
                                {offer.additionalKilometer}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Chip
                                label={t(offer.statusLocalized || offer.status)}
                                color={getStatusColor(offer.status) as any}
                                size="small"
                              />
                            </Grid>
                          </Grid>
                        </Paper>
                      ))}
                    </Stack>
                  </Collapse>
                </CardContent>
              </Card>
            )}

          {/* Additional Notes */}
          {businessBooking.additionalNotes && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <DescriptionIcon color="primary" />
                  <Typography variant="h6">
                    {t('businessBookings.details.additionalNotes')}
                  </Typography>
                  <IconButton size="small" onClick={() => toggleSection('notes')}>
                    {collapsedSections.notes ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                  </IconButton>
                </Box>
                <Collapse in={!collapsedSections.notes}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {businessBooking.additionalNotes}
                  </Typography>
                </Collapse>
              </CardContent>
            </Card>
          )}

          {/* Pickup Location Map */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <LocationIcon color="primary" />
                <Typography variant="h6">
                  {t('businessBookings.details.pickupLocationMap')}
                </Typography>
              </Box>
              <Box sx={{ height: '300px', borderRadius: 1, overflow: 'hidden' }}>
                {/* For now, show a placeholder since we don't have exact coordinates */}
                {/* In a real implementation, you would need to get coordinates for the pickup city */}
                <Box
                  sx={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'grey.100',
                    border: '1px solid',
                    borderColor: 'grey.300',
                  }}
                >
                  <Stack spacing={2} alignItems="center">
                    <LocationIcon sx={{ fontSize: 48, color: 'grey.500' }} />
                    <Typography variant="h6" color="text.secondary">
                      {t('businessBookings.details.pickupLocation')}:{' '}
                      {isRTL ? businessBooking.arPickUpCityName : businessBooking.enPickUpCityName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('businessBookings.details.mapPlaceholder')}
                    </Typography>
                  </Stack>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Modals */}
      <BusinessBookingTimelineModal
        open={timelineModalOpen}
        onClose={() => setTimelineModalOpen(false)}
        bookingId={bookingId}
      />

      <BusinessBookingChangeStatusModal
        open={statusModalOpen}
        onClose={() => setStatusModalOpen(false)}
        bookingId={bookingId}
        currentStatus={businessBooking?.status || ''}
        onSuccess={handleModalSuccess}
      />
    </Box>
  );
}
