'use client';
import { useState, useEffect } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import { useRouter } from 'next/navigation';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  IconButton,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { ArrowBack, Timeline as TimelineIcon, Edit as EditIcon } from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BUSINESS_RENTAL_DETAILS_QUERY } from '../../gql/queries/businessBookings';
import { GET_CUSTOMER_DETAILS_QUERY } from '../../gql/queries/customers';

// Import components that match old dashboard structure
import InfoCard from '../../shared/components/InfoCard';
import CustomerDataDisplay from '../../shared/components/CustomerDataDisplay';
import BusinessBookingTimelineModal from '../components/BusinessBookingTimelineModal';
import BusinessBookingChangeStatusModal from '../components/BusinessBookingChangeStatusModal';
import CustomTable from '../../shared/components/CustomTable';
import BusinessBookingPriceSummary from '../components/BusinessBookingPriceSummary';

// Table structure for offers (matching old dashboard exactly)
const offersTableData = [
  {
    headerId: 'business.offerId',
    dataRef: 'business.offerId',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.offerPrice',
    dataRef: 'business.offerPrice',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.carInsurance',
    dataRef: 'business.carInsurance',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.insuranceValue',
    dataRef: 'business.insuranceValue',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.monthlyInsuranceValue',
    dataRef: 'business.monthlyInsuranceValue',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.kilometerAllowed',
    dataRef: 'business.kilometerAllowed',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.additionalDistanceCost',
    dataRef: 'business.additionalDistanceCost',
    dataType: 'TEXT',
  },
  {
    headerId: 'business.offerDate',
    dataRef: 'business.offerDate',
    dataType: 'TEXT',
  },
  {
    headerId: 'bookings.list.allyName',
    dataRef: 'bookings.list.allyName',
    dataType: 'TEXT',
  },
  {
    headerId: 'status',
    dataRef: 'status',
    dataType: 'TEXT',
  },
];

interface BusinessBookingDetailsContentProps {
  bookingId: string;
}

export default function BusinessBookingDetailsContent({
  bookingId,
}: BusinessBookingDetailsContentProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const router = useRouter();

  // State for processed data (matching old dashboard exactly)
  const [requestDetails, setRequestDetails] = useState<any[]>([]);
  const [offersDetails, setOffersDetails] = useState<any[]>([]);

  // Modal states
  const [timelineModalOpen, setTimelineModalOpen] = useState(false);
  const [statusModalOpen, setStatusModalOpen] = useState(false);

  // Fetch business booking details
  const { data, loading, error, refetch } = useQuery(BUSINESS_RENTAL_DETAILS_QUERY, {
    variables: { id: bookingId },
    errorPolicy: 'all',
  });

  // Fetch customer details
  const { data: customerDetailsRes } = useQuery(GET_CUSTOMER_DETAILS_QUERY, {
    skip: !data?.businessRentalDetails?.userId,
    variables: { id: data?.businessRentalDetails?.userId },
  });

  const businessBooking = data?.businessRentalDetails;

  // Process data exactly like old dashboard
  useEffect(() => {
    if (businessBooking) {
      const {
        id,
        makeName,
        modelName,
        year,
        numberOfCars,
        pickUpCityName,
        numberOfMonths,
        pickUpDatetime,
        insuranceName,
        additionalNotes,
        arMakeName,
        enMakeName,
        arModelName,
        enModelName,
        arPickUpCityName,
        enPickUpCityName,
        arBusinessActivity,
        enBusinessActivity,
      } = businessBooking;

      // Create request details exactly like old dashboard
      const requestDataDetails = [
        { msgId: 'business.requestId', value: id },
        { msgId: 'car.make', value: isRTL ? arMakeName : enMakeName || makeName },
        { msgId: 'car.model', value: isRTL ? arModelName : enModelName || modelName },
        { msgId: 'car.year', value: year },
        { msgId: 'business.carNumbers', value: numberOfCars },
        { msgId: 'City', value: isRTL ? arPickUpCityName : enPickUpCityName || pickUpCityName },
        { msgId: 'business.durationMonths', value: numberOfMonths },
        {
          msgId: 'business.expectedPickupDate',
          value: pickUpDatetime
            ? format(new Date(pickUpDatetime), 'PPP', { locale: dateLocale })
            : '-',
        },
        { msgId: 'business.insuranceType', value: insuranceName },
        { msgId: 'business.additionalNotes', value: additionalNotes },
      ];
      setRequestDetails(requestDataDetails);

      // Process offers exactly like old dashboard
      if (businessBooking?.businessRentalOffers?.length) {
        const list = [];
        for (const offer of businessBooking.businessRentalOffers) {
          const offerDataDetails = {
            'business.offerId': offer?.id,
            'business.offerPrice': offer?.offerPrice,
            'business.carInsurance': offer?.carInsuranceStandard ? t('business.standard') : t('business.full'),
            'business.insuranceValue': offer?.carInsuranceStandard,
            'business.monthlyInsuranceValue': offer?.carInsuranceFull,
            'business.kilometerAllowed': offer?.kilometerPerMonth,
            'business.additionalDistanceCost': offer?.additionalKilometer,
            'business.offerDate': offer?.createdAt ? format(new Date(offer.createdAt), 'dd/MM/yyyy') : '-',
            'bookings.list.allyName': offer?.allyCompanyName,
            status: offer?.statusLocalized,
          };
          list.push(offerDataDetails);
        }
        setOffersDetails(list);
      }
    }
  }, [businessBooking, isRTL, t, dateLocale]);

  const handleBack = () => {
    router.push('/business-bookings');
  };

  const handleModalSuccess = () => {
    refetch();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !businessBooking) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {t('Error loading business booking details. Please try again later.')}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      {/* Header - matching old dashboard */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <IconButton onClick={handleBack}>
          <ArrowBack />
        </IconButton>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          {t('businessRental.details')}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {businessBooking.bookingNo || businessBooking.id}
        </Typography>
      </Box>

      {/* Action buttons */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          startIcon={<TimelineIcon />}
          onClick={() => setTimelineModalOpen(true)}
        >
          {t('businessBookings.actions.timeline')}
        </Button>
        <Button
          variant="contained"
          color="secondary"
          startIcon={<EditIcon />}
          onClick={() => setStatusModalOpen(true)}
        >
          {t('changeStatus')}
        </Button>
      </Box>

      {/* Main content - exactly like old dashboard */}
      <Grid container spacing={3}>
        {/* Left Column - Customer and Request Details */}
        <Grid item xs={12} md={6}>
          <CustomerDataDisplay customerDetailsRes={customerDetailsRes} withimages={false} />
        </Grid>

        {/* Right Column - Request Details */}
        <Grid item xs={12} md={6}>
          <InfoCard fullwidth data={requestDetails} titleId="request.details" />
        </Grid>

        {/* Full Width - Offers Table */}
        {offersDetails?.length > 0 && (
          <Grid item xs={12}>
            <CustomTable
              tableData={offersTableData}
              tableRecords={offersDetails}
              title="business.offers"
            />
          </Grid>
        )}
      </Grid>

      {/* Modals */}
      <BusinessBookingTimelineModal
        open={timelineModalOpen}
        onClose={() => setTimelineModalOpen(false)}
        bookingId={bookingId}
      />

      <BusinessBookingChangeStatusModal
        open={statusModalOpen}
        onClose={() => setStatusModalOpen(false)}
        bookingId={bookingId}
        currentStatus={businessBooking?.status || ''}
        onSuccess={handleModalSuccess}
      />
    </Box>
  );
}

          {/* Business Booking Details */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <BusinessIcon color="primary" />
                <Typography variant="h6">
                  {t('businessBookings.details.businessDetails')}
                </Typography>
                <IconButton size="small" onClick={() => toggleSection('businessDetails')}>
                  {collapsedSections.businessDetails ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                </IconButton>
              </Box>
              <Collapse in={!collapsedSections.businessDetails}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                      <Typography variant="h6">{t('Status')}</Typography>
                      <Chip
                        label={t(businessBooking.statusLocalized || businessBooking.status)}
                        color={getStatusColor(businessBooking.status) as any}
                        variant="filled"
                      />
                    </Box>
                    <Stack spacing={1}>
                      <Typography variant="body2">
                        {t('businessBookings.details.businessActivity')}:{' '}
                        {isRTL
                          ? businessBooking.arBusinessActivity
                          : businessBooking.enBusinessActivity}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.carDetails')}:{' '}
                        {isRTL ? businessBooking.arMakeName : businessBooking.enMakeName}{' '}
                        {isRTL ? businessBooking.arModelName : businessBooking.enModelName}{' '}
                        {businessBooking.year}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.numberOfCars')}: {businessBooking.numberOfCars}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.numberOfMonths')}:{' '}
                        {businessBooking.numberOfMonths}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.pickupLocation')}:{' '}
                        {isRTL
                          ? businessBooking.arPickUpCityName
                          : businessBooking.enPickUpCityName}
                      </Typography>
                      <Typography variant="body2">
                        {t('businessBookings.details.pickupDate')}:{' '}
                        {formatDate(businessBooking.pickUpDatetime)}
                      </Typography>
                      {businessBooking.dropOffDatetime && (
                        <Typography variant="body2">
                          {t('businessBookings.details.dropOffDate')}:{' '}
                          {formatDate(businessBooking.dropOffDatetime)}
                        </Typography>
                      )}
                      <Typography variant="body2">
                        {t('businessBookings.details.insurance')}: {businessBooking.insuranceName}
                      </Typography>
                      {businessBooking.pricePerMonth && (
                        <Typography variant="body2">
                          {t('businessBookings.details.pricePerMonth')}:{' '}
                          {formatCurrency(businessBooking.pricePerMonth)}
                        </Typography>
                      )}
                      {businessBooking.totalBookingPrice && (
                        <Typography variant="body2">
                          {t('businessBookings.details.totalPrice')}:{' '}
                          {formatCurrency(businessBooking.totalBookingPrice)}
                        </Typography>
                      )}
                      <Typography variant="body2" color="text.secondary">
                        {t('Created')}: {formatDate(businessBooking.createdAt)}
                      </Typography>
                      {businessBooking.cancelledAt && (
                        <Typography variant="body2" color="error">
                          {t('Cancelled')}: {formatDate(businessBooking.cancelledAt)}
                        </Typography>
                      )}
                      {businessBooking.closedAt && (
                        <Typography variant="body2" color="success.main">
                          {t('Closed')}: {formatDate(businessBooking.closedAt)}
                        </Typography>
                      )}
                    </Stack>
                  </Grid>
                </Grid>
              </Collapse>
            </CardContent>
          </Card>

          {/* Price Summary */}
          <BusinessBookingPriceSummary businessBooking={businessBooking} />
        </Grid>

        {/* Right Column - Full width on mobile, half on desktop */}
        <Grid item xs={12} lg={6}>
          {/* Accepted Offer Details */}
          {businessBooking.acceptedOffer && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <OfferIcon color="primary" />
                  <Typography variant="h6">
                    {t('businessBookings.details.acceptedOffer')}
                  </Typography>
                  <IconButton size="small" onClick={() => toggleSection('acceptedOffer')}>
                    {collapsedSections.acceptedOffer ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                  </IconButton>
                </Box>
                <Collapse in={!collapsedSections.acceptedOffer}>
                  <Stack spacing={2}>
                    <Typography variant="body2">
                      {t('businessBookings.details.offerPrice')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.offerPrice)}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.allyCompany')}:{' '}
                      {businessBooking.acceptedOffer.allyCompanyName}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.kilometerPerMonth')}:{' '}
                      {businessBooking.acceptedOffer.kilometerPerMonth}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.additionalKilometer')}:{' '}
                      {businessBooking.acceptedOffer.additionalKilometer}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.carInsuranceStandard')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.carInsuranceStandard)}
                    </Typography>
                    <Typography variant="body2">
                      {t('businessBookings.details.carInsuranceFull')}:{' '}
                      {formatCurrency(businessBooking.acceptedOffer.carInsuranceFull)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('businessBookings.details.offerDate')}:{' '}
                      {formatDate(businessBooking.acceptedOffer.createdAt)}
                    </Typography>
                  </Stack>
                </Collapse>
              </CardContent>
            </Card>
          )}

          {/* All Offers */}
          {businessBooking.businessRentalOffers &&
            businessBooking.businessRentalOffers.length > 0 && (
              <Card sx={{ mb: 3 }}>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                    <OfferIcon color="secondary" />
                    <Typography variant="h6">
                      {t('businessBookings.details.allOffers')} (
                      {businessBooking.businessRentalOffers.length})
                    </Typography>
                    <IconButton size="small" onClick={() => toggleSection('allOffers')}>
                      {collapsedSections.allOffers ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                    </IconButton>
                  </Box>
                  <Collapse in={!collapsedSections.allOffers}>
                    <Stack spacing={2}>
                      {businessBooking.businessRentalOffers.map((offer: any, index: number) => (
                        <Paper key={offer.id} sx={{ p: 2, backgroundColor: 'grey.50' }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>
                            {t('businessBookings.details.offer')} #{index + 1} -{' '}
                            {offer.allyCompanyName}
                          </Typography>
                          <Grid container spacing={1}>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.offerPrice')}:{' '}
                                {formatCurrency(offer.offerPrice)}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.kilometerPerMonth')}:{' '}
                                {offer.kilometerPerMonth}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Typography variant="body2">
                                {t('businessBookings.details.additionalKilometer')}:{' '}
                                {offer.additionalKilometer}
                              </Typography>
                            </Grid>
                            <Grid item xs={6}>
                              <Chip
                                label={t(offer.statusLocalized || offer.status)}
                                color={getStatusColor(offer.status) as any}
                                size="small"
                              />
                            </Grid>
                          </Grid>
                        </Paper>
                      ))}
                    </Stack>
                  </Collapse>
                </CardContent>
              </Card>
            )}

          {/* Additional Notes */}
          {businessBooking.additionalNotes && (
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                  <DescriptionIcon color="primary" />
                  <Typography variant="h6">
                    {t('businessBookings.details.additionalNotes')}
                  </Typography>
                  <IconButton size="small" onClick={() => toggleSection('notes')}>
                    {collapsedSections.notes ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                  </IconButton>
                </Box>
                <Collapse in={!collapsedSections.notes}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {businessBooking.additionalNotes}
                  </Typography>
                </Collapse>
              </CardContent>
            </Card>
          )}

          {/* Pickup Location Map */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <LocationIcon color="primary" />
                <Typography variant="h6">
                  {t('businessBookings.details.pickupLocationMap')}
                </Typography>
              </Box>
              <Box sx={{ height: '300px', borderRadius: 1, overflow: 'hidden' }}>
                {/* For now, show a placeholder since we don't have exact coordinates */}
                {/* In a real implementation, you would need to get coordinates for the pickup city */}
                <Box
                  sx={{
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'grey.100',
                    border: '1px solid',
                    borderColor: 'grey.300',
                  }}
                >
                  <Stack spacing={2} alignItems="center">
                    <LocationIcon sx={{ fontSize: 48, color: 'grey.500' }} />
                    <Typography variant="h6" color="text.secondary">
                      {t('businessBookings.details.pickupLocation')}:{' '}
                      {isRTL ? businessBooking.arPickUpCityName : businessBooking.enPickUpCityName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {t('businessBookings.details.mapPlaceholder')}
                    </Typography>
                  </Stack>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Modals */}
      <BusinessBookingTimelineModal
        open={timelineModalOpen}
        onClose={() => setTimelineModalOpen(false)}
        bookingId={bookingId}
      />

      <BusinessBookingChangeStatusModal
        open={statusModalOpen}
        onClose={() => setStatusModalOpen(false)}
        bookingId={bookingId}
        currentStatus={businessBooking?.status || ''}
        onSuccess={handleModalSuccess}
      />
    </Box>
  );
}
