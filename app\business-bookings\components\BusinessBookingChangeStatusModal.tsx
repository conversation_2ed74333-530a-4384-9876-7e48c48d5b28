'use client';
import { useState } from 'react';
import { useMutation, gql } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Close as CloseIcon, Edit as EditIcon } from '@mui/icons-material';
import { toast } from 'react-toastify';

interface BusinessBookingChangeStatusModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentStatus: string;
  onSuccess: () => void;
}

// Business Booking Status Change Mutations
const CONFIRM_BUSINESS_RENTAL = gql`
  mutation ConfirmBusinessRental($businessRentalId: ID!) {
    confirmBusinessRental(businessRentalId: $businessRentalId) {
      status
      errors
    }
  }
`;

const BUSINESS_CAR_RECEIVED = gql`
  mutation BusinessCarReceived($businessRentalId: ID!) {
    businessCarReceived(businessRentalId: $businessRentalId) {
      status
      errors
    }
  }
`;

const INVOICE_BUSINESS_RENTAL = gql`
  mutation InvoiceBusinessRental(
    $businessRentalId: ID!
    $invoicePic: String
    $newGrandTotal: Float
  ) {
    invoiceBusinessRental(
      businessRentalId: $businessRentalId
      invoicePic: $invoicePic
      newGrandTotal: $newGrandTotal
    ) {
      status
      errors
    }
  }
`;

const CLOSE_BUSINESS_RENTAL = gql`
  mutation CloseBusinessRental($businessRentalId: ID!, $closedReasons: ID, $closedReason: String) {
    closeBusinessRental(
      businessRentalId: $businessRentalId
      closedReasons: $closedReasons
      closedReason: $closedReason
    ) {
      status
      errors
    }
  }
`;

const REJECT_BUSINESS_RENTAL = gql`
  mutation RejectRental($rejectedReasons: ID!, $rentalId: ID!) {
    rejectRental(rejectedReasons: $rejectedReasons, rentalId: $rentalId) {
      status
      errors
    }
  }
`;

export default function BusinessBookingChangeStatusModal({
  open,
  onClose,
  bookingId,
  currentStatus,
  onSuccess,
}: BusinessBookingChangeStatusModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [newStatus, setNewStatus] = useState('');
  const [reason, setReason] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Define mutations
  const [confirmBusinessRental] = useMutation(CONFIRM_BUSINESS_RENTAL);
  const [businessCarReceived] = useMutation(BUSINESS_CAR_RECEIVED);
  const [invoiceBusinessRental] = useMutation(INVOICE_BUSINESS_RENTAL);
  const [closeBusinessRental] = useMutation(CLOSE_BUSINESS_RENTAL);
  const [rejectBusinessRental] = useMutation(REJECT_BUSINESS_RENTAL);

  const statusOptions = [
    { value: 'pending', label: t('Pending') },
    { value: 'confirmed', label: t('Confirmed') },
    { value: 'accepted', label: t('Accepted') },
    { value: 'rejected', label: t('Rejected') },
    { value: 'cancelled', label: t('Cancelled') },
    { value: 'closed', label: t('Closed') },
  ];

  const handleClose = () => {
    setNewStatus('');
    setReason('');
    setError('');
    onClose();
  };

  const handleSubmit = async () => {
    if (!newStatus) {
      setError(t('businessBookings.selectStatus'));
      return;
    }

    if ((newStatus === 'rejected' || newStatus === 'cancelled') && !reason.trim()) {
      setError(t('businessBookings.reasonRequired'));
      return;
    }

    setError('');
    setLoading(true);

    try {
      let result;

      switch (newStatus) {
        case 'confirmed':
          result = await confirmBusinessRental({
            variables: { businessRentalId: bookingId },
          });
          break;

        case 'car_received':
          result = await businessCarReceived({
            variables: { businessRentalId: bookingId },
          });
          break;

        case 'invoiced':
          result = await invoiceBusinessRental({
            variables: {
              businessRentalId: bookingId,
              invoicePic: '',
              newGrandTotal: 0,
            },
          });
          break;

        case 'closed':
          result = await closeBusinessRental({
            variables: {
              businessRentalId: bookingId,
              closedReasons: null, // This would need to be selected from a list
              closedReason: reason.trim() || null,
            },
          });
          break;

        case 'rejected':
        case 'cancelled':
          result = await rejectBusinessRental({
            variables: {
              rentalId: bookingId,
              rejectedReasons: 1, // This would need to be selected from a list
            },
          });
          break;

        default:
          throw new Error(t('Unsupported status'));
      }

      // Check if the mutation was successful
      const mutationData = Object.values(result.data)[0] as any;
      if (mutationData.status === 'success' || !mutationData.errors?.length) {
        toast.success(t('businessBookings.statusChangedSuccess'));
        onSuccess();
        handleClose();
      } else {
        setError(mutationData.errors?.[0] || t('businessBookings.statusChangeFailed'));
      }
    } catch (err: any) {
      console.error('Error changing status:', err);
      setError(err.message || t('businessBookings.statusChangeFailed'));
      toast.error(t('businessBookings.statusChangeFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <EditIcon color="primary" />
          <Typography variant="h6">{t('businessBookings.changeStatus.title')}</Typography>
        </Box>
        <Button onClick={handleClose} sx={{ minWidth: 'auto', p: 1 }} color="inherit">
          <CloseIcon />
        </Button>
      </DialogTitle>

      <DialogContent sx={{ px: 3, py: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <Typography variant="body2" color="text.secondary">
            {t('businessBookings.changeStatus.currentStatus')}: <strong>{t(currentStatus)}</strong>
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <FormControl fullWidth>
            <InputLabel>{t('businessBookings.changeStatus.newStatus')}</InputLabel>
            <Select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              label={t('businessBookings.changeStatus.newStatus')}
            >
              {statusOptions
                .filter((option) => option.value !== currentStatus)
                .map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          {(newStatus === 'rejected' || newStatus === 'cancelled') && (
            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('businessBookings.changeStatus.reason')}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t('businessBookings.changeStatus.reasonPlaceholder')}
              required
            />
          )}

          {newStatus && newStatus !== 'rejected' && newStatus !== 'cancelled' && (
            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('businessBookings.changeStatus.notes')}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t('businessBookings.changeStatus.notesPlaceholder')}
            />
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleClose} variant="outlined" disabled={loading}>
          {t('Cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !newStatus}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {t('businessBookings.changeStatus.submit')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
