'use client';
import { useState } from 'react';
import { useMutation } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  CircularProgress,
} from '@mui/material';
import { Close as CloseIcon, Edit as EditIcon } from '@mui/icons-material';
import { toast } from 'react-toastify';

interface BusinessBookingChangeStatusModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
  currentStatus: string;
  onSuccess: () => void;
}

// Note: This would need to be implemented based on your actual GraphQL mutation
const CHANGE_BUSINESS_BOOKING_STATUS = `
  mutation ChangeBusinessBookingStatus($id: ID!, $status: String!, $reason: String) {
    changeBusinessBookingStatus(id: $id, status: $status, reason: $reason) {
      id
      status
      statusLocalized
    }
  }
`;

export default function BusinessBookingChangeStatusModal({
  open,
  onClose,
  bookingId,
  currentStatus,
  onSuccess,
}: BusinessBookingChangeStatusModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const [newStatus, setNewStatus] = useState('');
  const [reason, setReason] = useState('');
  const [error, setError] = useState('');

  // Note: This mutation would need to be implemented in your GraphQL schema
  const [changeStatus, { loading }] = useMutation(CHANGE_BUSINESS_BOOKING_STATUS, {
    onCompleted: () => {
      toast.success(t('businessBookings.statusChangedSuccess'));
      onSuccess();
      handleClose();
    },
    onError: (error) => {
      setError(error.message);
      toast.error(t('businessBookings.statusChangeFailed'));
    },
  });

  const statusOptions = [
    { value: 'pending', label: t('Pending') },
    { value: 'confirmed', label: t('Confirmed') },
    { value: 'accepted', label: t('Accepted') },
    { value: 'rejected', label: t('Rejected') },
    { value: 'cancelled', label: t('Cancelled') },
    { value: 'closed', label: t('Closed') },
  ];

  const handleClose = () => {
    setNewStatus('');
    setReason('');
    setError('');
    onClose();
  };

  const handleSubmit = () => {
    if (!newStatus) {
      setError(t('businessBookings.selectStatus'));
      return;
    }

    if ((newStatus === 'rejected' || newStatus === 'cancelled') && !reason.trim()) {
      setError(t('businessBookings.reasonRequired'));
      return;
    }

    setError('');
    changeStatus({
      variables: {
        id: bookingId,
        status: newStatus,
        reason: reason.trim() || null,
      },
    });
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <EditIcon color="primary" />
          <Typography variant="h6">{t('businessBookings.changeStatus.title')}</Typography>
        </Box>
        <Button onClick={handleClose} sx={{ minWidth: 'auto', p: 1 }} color="inherit">
          <CloseIcon />
        </Button>
      </DialogTitle>

      <DialogContent sx={{ px: 3, py: 2 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          <Typography variant="body2" color="text.secondary">
            {t('businessBookings.changeStatus.currentStatus')}: <strong>{t(currentStatus)}</strong>
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <FormControl fullWidth>
            <InputLabel>{t('businessBookings.changeStatus.newStatus')}</InputLabel>
            <Select
              value={newStatus}
              onChange={(e) => setNewStatus(e.target.value)}
              label={t('businessBookings.changeStatus.newStatus')}
            >
              {statusOptions
                .filter((option) => option.value !== currentStatus)
                .map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          {(newStatus === 'rejected' || newStatus === 'cancelled') && (
            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('businessBookings.changeStatus.reason')}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t('businessBookings.changeStatus.reasonPlaceholder')}
              required
            />
          )}

          {newStatus && newStatus !== 'rejected' && newStatus !== 'cancelled' && (
            <TextField
              fullWidth
              multiline
              rows={3}
              label={t('businessBookings.changeStatus.notes')}
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              placeholder={t('businessBookings.changeStatus.notesPlaceholder')}
            />
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={handleClose} variant="outlined" disabled={loading}>
          {t('Cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !newStatus}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {t('businessBookings.changeStatus.submit')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
