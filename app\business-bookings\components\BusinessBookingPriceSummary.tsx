'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Stack,
  Divider,
  Chip,
  Paper,
  Grid,
} from '@mui/material';
import {
  AttachMoney as PriceIcon,
  LocalOffer as OfferIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';

interface BusinessBookingPriceSummaryProps {
  businessBooking: any;
}

export default function BusinessBookingPriceSummary({
  businessBooking,
}: BusinessBookingPriceSummaryProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';

  const formatCurrency = (amount: number) => {
    if (!amount) return '0';
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'accepted':
        return 'success';
      case 'rejected':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (!businessBooking) {
    return null;
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          <ReceiptIcon color="primary" />
          <Typography variant="h6">{t('businessBookings.priceSummary.title')}</Typography>
        </Box>

        {/* Basic Information */}
        <Paper sx={{ p: 2, mb: 3, backgroundColor: 'grey.50' }}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
            {t('businessBookings.priceSummary.basicInfo')}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                {t('businessBookings.details.numberOfCars')}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {businessBooking.numberOfCars}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                {t('businessBookings.details.numberOfMonths')}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: 500 }}>
                {businessBooking.numberOfMonths}
              </Typography>
            </Grid>
            {businessBooking.pricePerMonth && (
              <Grid item xs={12}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.pricePerMonth')}
                </Typography>
                <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                  {formatCurrency(businessBooking.pricePerMonth)}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Accepted Offer Summary */}
        {businessBooking.acceptedOffer && (
          <Paper
            sx={{
              p: 2,
              mb: 3,
              backgroundColor: 'success.50',
              border: '1px solid',
              borderColor: 'success.200',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <OfferIcon color="success" />
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {t('businessBookings.priceSummary.acceptedOffer')}
              </Typography>
              <Chip label={t('Accepted')} color="success" size="small" />
            </Box>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.allyCompany')}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {businessBooking.acceptedOffer.allyCompanyName}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.offerPrice')}
                </Typography>
                <Typography variant="h6" color="success.main" sx={{ fontWeight: 600 }}>
                  {formatCurrency(businessBooking.acceptedOffer.offerPrice)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.kilometerPerMonth')}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {businessBooking.acceptedOffer.kilometerPerMonth} {t('businessBookings.km')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.additionalKilometer')}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {formatCurrency(businessBooking.acceptedOffer.additionalKilometer)} /{' '}
                  {t('businessBookings.km')}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.carInsuranceStandard')}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {formatCurrency(businessBooking.acceptedOffer.carInsuranceStandard)}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  {t('businessBookings.details.carInsuranceFull')}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {formatCurrency(businessBooking.acceptedOffer.carInsuranceFull)}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        )}

        {/* Total Summary */}
        {businessBooking.totalBookingPrice && (
          <Paper
            sx={{
              p: 2,
              backgroundColor: 'primary.50',
              border: '1px solid',
              borderColor: 'primary.200',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <PriceIcon color="primary" />
              <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                {t('businessBookings.priceSummary.totalSummary')}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {t('businessBookings.details.totalPrice')}
              </Typography>
              <Typography variant="h5" color="primary" sx={{ fontWeight: 700 }}>
                {formatCurrency(businessBooking.totalBookingPrice)}
              </Typography>
            </Box>
          </Paper>
        )}

        {/* All Offers Summary */}
        {businessBooking.businessRentalOffers &&
          businessBooking.businessRentalOffers.length > 1 && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                {t('businessBookings.priceSummary.allOffers')} (
                {businessBooking.businessRentalOffers.length})
              </Typography>
              <Stack spacing={1}>
                {businessBooking.businessRentalOffers.map((offer: any, index: number) => (
                  <Paper key={offer.id} sx={{ p: 2, backgroundColor: 'grey.50' }}>
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <Box>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {offer.allyCompanyName}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {formatCurrency(offer.offerPrice)} - {offer.kilometerPerMonth}{' '}
                          {t('businessBookings.km')}/{t('businessBookings.month')}
                        </Typography>
                      </Box>
                      <Chip
                        label={t(offer.statusLocalized || offer.status)}
                        color={getStatusColor(offer.status) as any}
                        size="small"
                      />
                    </Box>
                  </Paper>
                ))}
              </Stack>
            </Box>
          )}
      </CardContent>
    </Card>
  );
}
