'use client';
import { useState } from 'react';
import { useQuery } from '@apollo/client';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  Paper,
  useTheme,
} from '@mui/material';
import {
  Close as CloseIcon,
  Timeline as TimelineIcon,
  Edit as EditIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BUSINESS_RENTAL_AUDITS_QUERY } from '../../gql/queries/businessBookings';

interface BusinessBookingTimelineModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
}

export default function BusinessBookingTimelineModal({
  open,
  onClose,
  bookingId,
}: BusinessBookingTimelineModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;
  const theme = useTheme();

  const { data, loading, error } = useQuery(BUSINESS_RENTAL_AUDITS_QUERY, {
    variables: { id: bookingId },
    skip: !open,
    errorPolicy: 'all',
  });

  const audits = data?.businessRentalAudits || [];

  const formatDate = (date: string) => {
    if (!date) return '-';
    return format(new Date(date), 'PPp', { locale: dateLocale });
  };

  const getTimelineIcon = (audit: any) => {
    const newData = audit.newData ? JSON.parse(audit.newData) : {};
    const oldData = audit.oldData ? JSON.parse(audit.oldData) : {};

    if (newData.status !== oldData.status) {
      switch (newData.status?.toLowerCase()) {
        case 'accepted':
          return <CheckCircleIcon color="success" />;
        case 'rejected':
        case 'cancelled':
          return <CancelIcon color="error" />;
        default:
          return <EditIcon color="primary" />;
      }
    }
    return <InfoIcon color="info" />;
  };

  const getTimelineColor = (audit: any) => {
    const newData = audit.newData ? JSON.parse(audit.newData) : {};
    const oldData = audit.oldData ? JSON.parse(audit.oldData) : {};

    if (newData.status !== oldData.status) {
      switch (newData.status?.toLowerCase()) {
        case 'accepted':
          return 'success';
        case 'rejected':
        case 'cancelled':
          return 'error';
        default:
          return 'primary';
      }
    }
    return 'grey';
  };

  const getAuditDescription = (audit: any) => {
    const newData = audit.newData ? JSON.parse(audit.newData) : {};
    const oldData = audit.oldData ? JSON.parse(audit.oldData) : {};

    const changes = [];

    if (newData.status !== oldData.status) {
      changes.push(
        `${t('businessBookings.statusChangedFrom')} "${oldData.status || t('Unknown')}" ${t(
          'to'
        )} "${newData.status}"`
      );
    }

    if (newData.assignedTo !== oldData.assignedTo) {
      changes.push(
        `${t('businessBookings.assignmentChangedFrom')} "${
          oldData.assignedTo || t('Unassigned')
        }" ${t('to')} "${newData.assignedTo || t('Unassigned')}"`
      );
    }

    if (changes.length === 0) {
      changes.push(t('businessBookings.recordUpdated'));
    }

    return changes.join(', ');
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '80vh',
        },
      }}
    >
      <DialogTitle
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          pb: 2,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TimelineIcon color="primary" />
          <Typography variant="h6">{t('businessBookings.timeline.title')}</Typography>
        </Box>
        <Button onClick={onClose} sx={{ minWidth: 'auto', p: 1 }} color="inherit">
          <CloseIcon />
        </Button>
      </DialogTitle>

      <DialogContent sx={{ px: 3, py: 0 }}>
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {t('Error loading timeline data')}
          </Alert>
        )}

        {!loading && !error && audits.length === 0 && (
          <Alert severity="info" sx={{ mb: 2 }}>
            {t('businessBookings.noTimelineData')}
          </Alert>
        )}

        {!loading && !error && audits.length > 0 && (
          <Timeline position={isRTL ? 'right' : 'left'}>
            {audits.map((audit: any, index: number) => (
              <TimelineItem key={index}>
                <TimelineSeparator>
                  <TimelineDot color={getTimelineColor(audit) as any}>
                    {getTimelineIcon(audit)}
                  </TimelineDot>
                  {index < audits.length - 1 && <TimelineConnector />}
                </TimelineSeparator>
                <TimelineContent>
                  <Paper
                    elevation={1}
                    sx={{
                      p: 2,
                      mb: 2,
                      backgroundColor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.50',
                    }}
                  >
                    <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
                      {getAuditDescription(audit)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      {t('By')}: {audit.userName || t('System')}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatDate(audit.createdAt)}
                    </Typography>
                  </Paper>
                </TimelineContent>
              </TimelineItem>
            ))}
          </Timeline>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, py: 2 }}>
        <Button onClick={onClose} variant="outlined">
          {t('Close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
