'use client';
import React, { useEffect, useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { format } from 'date-fns';
import { ar, enUS } from 'date-fns/locale';
import { BUSINESS_RENTAL_AUDITS_QUERY } from '../../gql/queries/businessBookings';

interface BusinessRentalAudit {
  createdAt: string;
  newData?: string;
  oldData?: string;
  businessRentalId: string;
  userId: string;
  userName: string;
}

interface BusinessBookingsTimelineModalProps {
  open: boolean;
  onClose: () => void;
  bookingId: string;
}

export default function BusinessBookingsTimelineModal({
  open,
  onClose,
  bookingId,
}: BusinessBookingsTimelineModalProps) {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.language === 'ar';
  const dateLocale = isRTL ? ar : enUS;

  const [oldData, setOldData] = useState<any[]>([]);
  const [newData, setNewData] = useState<any[]>([]);

  const {
    data: businessRentalAudits,
    loading,
    error,
    refetch,
  } = useQuery(BUSINESS_RENTAL_AUDITS_QUERY, {
    skip: !bookingId,
    variables: { id: bookingId },
  });

  const isJSONString = (str: string): boolean => {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  };

  useEffect(() => {
    if (businessRentalAudits?.businessRentalAudits?.length) {
      const newDataArray: any[] = [];
      const oldDataArray: any[] = [];

      businessRentalAudits.businessRentalAudits.forEach((audit: BusinessRentalAudit) => {
        if (audit?.newData && isJSONString(audit.newData)) {
          newDataArray.push(JSON.parse(audit.newData));
        } else {
          newDataArray.push({});
        }

        if (audit?.oldData && isJSONString(audit.oldData)) {
          oldDataArray.push(JSON.parse(audit.oldData));
        } else {
          oldDataArray.push({});
        }
      });

      setOldData(oldDataArray);
      setNewData(newDataArray);
    }
  }, [businessRentalAudits]);

  useEffect(() => {
    if (bookingId && open) {
      refetch();
    }
  }, [bookingId, open, refetch]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy h:mm:ss a', { locale: dateLocale });
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      // Handle HHmmss format from old dashboard
      if (timeString && timeString.length === 6) {
        const hours = timeString.slice(0, 2);
        const minutes = timeString.slice(2, 4);
        const seconds = timeString.slice(4, 6);
        return `${hours}:${minutes}:${seconds}`;
      }
      return timeString;
    } catch {
      return timeString;
    }
  };

  // Field name translations for business bookings
  const getFieldLabel = (key: string): string => {
    const fieldTranslations: { [key: string]: string } = {
      // Business booking specific fields
      additional_notes: t('businessBookings.additionalNotes', { defaultValue: 'Additional Notes' }),
      ally_company_id: t('businessBookings.allyCompany', { defaultValue: 'Ally Company' }),
      business_activity_id: t('businessBookings.businessActivity', {
        defaultValue: 'Business Activity',
      }),
      car_model_id: t('businessBookings.carModel', { defaultValue: 'Car Model' }),
      car_version_id: t('businessBookings.carVersion', { defaultValue: 'Car Version' }),
      commercial_registration_certificate: t('businessBookings.commercialRegistration', {
        defaultValue: 'Commercial Registration',
      }),
      commercial_registration_no: t('businessBookings.commercialRegistrationNo', {
        defaultValue: 'Commercial Registration Number',
      }),
      company_email: t('businessBookings.companyEmail', { defaultValue: 'Company Email' }),
      company_name: t('businessBookings.companyName', { defaultValue: 'Company Name' }),
      company_phone: t('businessBookings.companyPhone', { defaultValue: 'Company Phone' }),
      insurance_id: t('businessBookings.insurance', { defaultValue: 'Insurance' }),
      make_id: t('businessBookings.make', { defaultValue: 'Make' }),
      number_of_cars: t('businessBookings.numberOfCars', { defaultValue: 'Number of Cars' }),
      number_of_months: t('businessBookings.numberOfMonths', { defaultValue: 'Number of Months' }),
      other_car_name: t('businessBookings.otherCarName', { defaultValue: 'Other Car Name' }),
      pick_up_city_id: t('businessBookings.pickUpCity', { defaultValue: 'Pick-up City' }),
      pick_up_datetime: t('businessBookings.pickUpDatetime', {
        defaultValue: 'Pick-up Date & Time',
      }),
      drop_off_datetime: t('businessBookings.dropOffDatetime', {
        defaultValue: 'Drop-off Date & Time',
      }),
      price_per_month: t('businessBookings.pricePerMonth', { defaultValue: 'Price Per Month' }),
      total_booking_price: t('businessBookings.totalPrice', {
        defaultValue: 'Total Booking Price',
      }),
      year: t('businessBookings.year', { defaultValue: 'Year' }),
      // Common fields
      status: t('common.status', { defaultValue: 'Status' }),
      notes: t('common.notes', { defaultValue: 'Notes' }),
      user_id: t('common.user', { defaultValue: 'User' }),
      created_at: t('common.createdAt', { defaultValue: 'Created At' }),
      updated_at: t('common.updatedAt', { defaultValue: 'Updated At' }),
    };

    return fieldTranslations[key] || t(key, { defaultValue: key.replace(/_/g, ' ') });
  };

  // Value translations and formatting for business bookings
  const formatFieldValue = (key: string, value: any): string => {
    if (!value || value === null || value === '-') return '-';

    // Status translations
    const statusTranslations: { [key: string]: string } = {
      pending: t('businessBookings.status.pending', { defaultValue: 'Pending' }),
      confirmed: t('businessBookings.status.confirmed', { defaultValue: 'Confirmed' }),
      car_received: t('businessBookings.status.carReceived', { defaultValue: 'Car Received' }),
      invoiced: t('businessBookings.status.invoiced', { defaultValue: 'Invoiced' }),
      closed: t('businessBookings.status.closed', { defaultValue: 'Closed' }),
      cancelled: t('businessBookings.status.cancelled', { defaultValue: 'Cancelled' }),
    };

    // Handle specific field types
    if (['pick_up_datetime', 'drop_off_datetime'].includes(key)) {
      try {
        return format(new Date(value), 'dd/MM/yyyy HH:mm', { locale: dateLocale });
      } catch {
        return value;
      }
    }

    if (['created_at', 'updated_at'].includes(key)) {
      try {
        return format(new Date(value), 'dd/MM/yyyy HH:mm:ss', { locale: dateLocale });
      } catch {
        return value;
      }
    }

    // Translate status values
    if (typeof value === 'string' && statusTranslations[value]) {
      return statusTranslations[value];
    }

    // Try to translate the value if it's a string
    if (typeof value === 'string') {
      return t(value, { defaultValue: value });
    }

    return String(value);
  };

  // Fields to hide from timeline (technical fields)
  const hideFields = [
    'id',
    'user_id',
    'ally_company_id',
    'business_activity_id',
    'car_model_id',
    'car_version_id',
    'insurance_id',
    'make_id',
    'pick_up_city_id',
  ];

  const renderDataValue = (key: string, value: any) => {
    if (!value || value === null) return '-';

    // Handle notes array
    if (key === 'additional_notes' && Array.isArray(value)) {
      return value.map((item: any, idx: number) => <div key={idx}>{item?.note || item}</div>);
    }

    return formatFieldValue(key, value);
  };

  const renderDataSection = (data: any, title: string) => (
    <Box sx={{ flex: 1, mx: 1 }}>
      <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
        {title}
      </Typography>
      <List dense>
        {data &&
          Object.entries(data)
            .filter(([key]) => !hideFields.includes(key)) // Filter out technical fields
            .map(([key, value]) => (
              <ListItem key={key} sx={{ px: 0, py: 0.5 }}>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {getFieldLabel(key)}:
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {renderDataValue(key, value)}
                      </Typography>
                    </Box>
                  }
                />
              </ListItem>
            ))}
      </List>
    </Box>
  );

  if (loading) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  if (error) {
    return (
      <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
        <DialogTitle>
          {t('businessBookings.timeline.title', { defaultValue: 'Business Booking Timeline' })}
        </DialogTitle>
        <DialogContent>
          <Alert severity="error">
            {t('error.loadingTimeline', { defaultValue: 'Error loading timeline data' })}
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>{t('common.close')}</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' },
      }}
    >
      <DialogTitle>
        <Typography variant="h6">
          {t('businessBookings.timeline.title', { defaultValue: 'Business Booking Timeline' })}
        </Typography>
      </DialogTitle>

      <DialogContent>
        {businessRentalAudits?.businessRentalAudits?.length ? (
          <Box sx={{ height: '500px', overflowY: 'auto' }}>
            {/* Booking ID Header */}
            <Box sx={{ mb: 3, p: 2, backgroundColor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                {t('businessBookings.details.bookingNumber', { defaultValue: 'Booking Number' })}:{' '}
                {businessRentalAudits.businessRentalAudits[0]?.businessRentalId}
              </Typography>
            </Box>

            {/* Timeline Items */}
            {businessRentalAudits.businessRentalAudits.map(
              (audit: BusinessRentalAudit, index: number) => (
                <Box key={index} sx={{ mb: 3 }}>
                  {/* Audit Header */}
                  <Box
                    sx={{
                      mb: 2,
                      p: 2,
                      backgroundColor: 'primary.main',
                      color: 'white',
                      borderRadius: 1,
                    }}
                  >
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      {audit.userName}
                    </Typography>
                    <Typography variant="body2">{formatDate(audit.createdAt)}</Typography>
                  </Box>

                  {/* Data Comparison */}
                  <Box sx={{ display: 'flex', gap: 2 }}>
                    {renderDataSection(
                      oldData[index],
                      t('businessBookings.timeline.oldData', { defaultValue: 'Old Data' })
                    )}
                    <Divider orientation="vertical" flexItem />
                    {renderDataSection(
                      newData[index],
                      t('businessBookings.timeline.newData', { defaultValue: 'New Data' })
                    )}
                  </Box>

                  {index < businessRentalAudits.businessRentalAudits.length - 1 && (
                    <Divider sx={{ my: 3 }} />
                  )}
                </Box>
              )
            )}
          </Box>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <Typography variant="body1">
              {t('businessBookings.timeline.noDataFound', {
                defaultValue: 'No timeline data found',
              })}
            </Typography>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="contained">
          {t('common.close')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
