import { gql } from '@apollo/client';

export const GET_CUSTOMER_BY_PHONE = gql`
  query GetCustomerByPhone($mobile: String!, $type: String) {
    users(limit: 1, mobile: $mobile, type: $type) {
      collection {
        id
        name
        mobile
        email
        isActive
        createdAt
        customerProfile {
          nid
          firstName
          lastName
          middleName
          dob
          nationality {
            arName
            enName
            id
            name
          }
          city {
            arName
            enName
            id
            name
          }
          driverLicense
          driverLicenseExpireAt
          driverLicenseStatus
          status
          title
          gender
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const GET_ALL_CUSTOMERS = gql`
  query GetAllCustomers($limit: Int, $page: Int, $name: String, $isActive: Boolean) {
    users(limit: $limit, page: $page, name: $name, isActive: $isActive) {
      collection {
        id
        name
        mobile
        email
        isActive
        createdAt
        customerProfile {
          nid
          firstName
          lastName
          middleName
          dob
          nationality {
            arName
            enName
            id
            name
          }
          city {
            arName
            enName
            id
            name
          }
          driverLicense
          driverLicenseExpireAt
          driverLicenseStatus
          status
          title
          gender
        }
      }
      metadata {
        currentPage
        limitValue
        totalCount
        totalPages
      }
    }
  }
`;

export const CREATE_CUSTOMER = gql`
  mutation CreateCustomer(
    $name: String!
    $mobile: String!
    $email: String
    $nid: String
    $firstName: String
    $lastName: String
    $middleName: String
    $dob: String
    $nationality: String
    $city: String
    $driverLicense: String
    $driverLicenseExpireAt: String
    $title: String
    $gender: String
  ) {
    createUser(
      input: {
        name: $name
        mobile: $mobile
        email: $email
        customerProfile: {
          nid: $nid
          firstName: $firstName
          lastName: $lastName
          middleName: $middleName
          dob: $dob
          nationality: $nationality
          city: $city
          driverLicense: $driverLicense
          driverLicenseExpireAt: $driverLicenseExpireAt
          title: $title
          gender: $gender
        }
      }
    ) {
      user {
        id
        name
        mobile
        email
        isActive
        createdAt
        customerProfile {
          nid
          firstName
          lastName
          middleName
          dob
          nationality {
            arName
            enName
            id
            name
          }
          city {
            arName
            enName
            id
            name
          }
          driverLicense
          driverLicenseExpireAt
          driverLicenseStatus
          status
          title
          gender
        }
      }
      errors
    }
  }
`;

// Customer Details Query - matching old dashboard
export const GET_CUSTOMER_DETAILS_QUERY = gql`
  query GetCustomerDetailsQuery($id: ID!) {
    user(id: $id) {
      id
      name
      mobile
      email
      isActive
      createdAt
      customerProfile {
        nid
        firstName
        lastName
        middleName
        dob
        nationality {
          arName
          enName
          id
          name
        }
        city {
          arName
          enName
          id
          name
        }
        driverLicense
        driverLicenseExpireAt
        driverLicenseStatus
        status
        title
        gender
        companyName
        businessCard
        customerClass
        customerClassLocalized
        email
        blockingStatus
        blockingReason
      }
    }
  }
`;
