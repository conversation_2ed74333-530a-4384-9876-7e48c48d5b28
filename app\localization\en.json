{"login.welcome": "Welcome", "login.login": "<PERSON><PERSON>", "login.email": "Email", "login.password": "Password", "login.Signing in...": "Signing in...", "login.Sign In": "Sign In", "login.passwordTooShort": "Password must be at least 8 characters long", "login.invalidEmail": "Please enter a valid email address", "login.requestTimeout": "The request timed out. Please try again later.", "login.networkOffline": "You appear to be offline. Please check your internet connection.", "login.tooManyRequests": "Too many requests. Please try again later.", "login.networkError": "Network error. Please check your connection and try again.", "login.invalidCredentials": "Invalid email or password", "login.unknownError": "An unknown error occurred. Please try again.", "login.invalidResponse": "Invalid response from server", "login.invalidToken": "Invalid authentication token", "login.storageError": "Failed to store authentication data", "login.tooManyAttempts": "Too many login attempts. Please wait a moment and try again.", "common.actions": "Actions", "branchname": "Branch Name", "daily Price": "daily Price", "car.availabilityStatus": "Car Availability Status", "Transmission": "Transmission", "City Name": "City Name", "Car Count": "Car Count", "error.graphql": "An error occurred while processing your request", "error.forbidden": "You do not have permission to perform this action", "error.notFound": "The requested resource was not found", "error.serverError": "Server error. Please try again later.", "error.validation": "Validation error. Please check your input.", "error.duplicate": "A duplicate entry already exists", "error.timeout": "The request timed out. Please try again.", "common.cancel": "Cancel", "common.back": "Back", "common.close": "Close", "common.from": "From", "common.to": "To", "common.reason": "Reason", "common.characters": "characters", "common.notes": "Notes", "common.status": "Status", "common.user": "User", "common.yes": "Yes", "common.no": "No", "common.next": "Next", "customer.information": "Customer Information", "booking.details": "Booking Details", "vehicle.selection": "Vehicle Selection", "services.payment": "Services & Payment", "summary.confirmation": "Summary & Confirmation", "please.complete.required.fields": "Please complete all required fields", "Please fill in all required fields": "Please fill in all required fields", "price.summary": "Price Summary", "car.rental": "Car Rental", "extra.services": "Extra Services", "total": "Total", "currency.sr": "SR", "select.car.to.see.pricing": "Select a car to see pricing", "This field is required": "This field is required", "customerNotFound": "Customer not found", "errorSearchingCustomer": "Error searching for customer", "pleaseEnterPhoneNumber": "Please enter a phone number", "pleaseEnterValidPhoneNumber": "Please enter a valid phone number", "customerNotFoundWithPhone": "No customer found with phone number: {{phone}}", "createNewCustomer": "Create New Customer", "customerFound": "Customer Found", "notSpecified": "Not Specified", "accountStatus": "Account Status", "active": "Active", "inactive": "Inactive", "loyaltyProgramStatus": "Loyalty Program Status", "fursanVerificationWillBeImplemented": "Fursan verification will be implemented", "enterCustomerPhoneInstructions": "Enter the customer's phone number to search for their account", "select.pickup.branch": "Select Pickup Branch", "select.dropoff.branch": "Select Drop-off Branch", "selecting.company.first": "Select company first", "select.pickup.branch.first": "Select pickup branch first", "select.car": "Select Car", "select.branch.first": "Select branch first", "insurances.available": "insurances available", "selected.car": "Selected Car", "plate.number": "Plate Number", "color": "Color", "available.insurances": "Available Insurances", "select.insurance": "Select Insurance", "select.car.first.to.see.insurances": "Select a car first to see available insurances", "no.insurances.available.for.this.car": "No insurances available for this car", "selected.insurance": "Selected Insurance", "insurance.price": "Insurance Price", "no.extra.services.available": "No extra services available", "required": "Required", "selected.services.summary": "Selected Services Summary", "delivery.options": "Delivery Options", "car.delivery": "Car Delivery", "deliver.car.to.your.location": "Deliver car to your location", "delivery.address": "Delivery Address", "enter.delivery.address": "Enter delivery address", "share.current.location": "Share Current Location", "map.will.be.displayed.here": "Map will be displayed here", "coordinates": "Coordinates", "delivery.price": "Delivery Price", "two.way.delivery": "Two-way Delivery", "pick.up.car.from.dropoff.location": "Pick up car from drop-off location", "handover.location": "Handover Location", "handover.address": "Handover Address", "enter.handover.address": "Enter handover address", "handover.map.will.be.displayed.here": "Handover map will be displayed here", "handover.price": "Handover Price", "delivery.summary": "Delivery Summary", "not.specified": "Not specified", "total.delivery.cost": "Total Delivery Cost", "delivery.pickup.location": "Delivery & Pickup Location", "select.delivery.location.description": "Click on the map or search to select your delivery location", "search.location": "Search Location", "please.select.delivery.location": "Please select a delivery location", "selected.location": "Selected Location", "click.on.map.to.set.location": "Click on the map to set location", "drag.marker.to.adjust.position": "Drag marker to adjust position", "use.search.to.find.specific.place": "Use search to find a specific place", "use.my.location.for.current.position": "Use 'My Location' for current position", "two.way.delivery.note": "Two-way Delivery Note", "car.will.be.delivered.to.this.location.and.picked.up.from.same.location": "Car will be delivered to this location and picked up from the same location", "geolocation.not.supported": "Geolocation is not supported by this browser", "error.getting.location": "Error getting your location", "payment.method": "Payment Method", "payment.cash": "Cash", "payment.online": "Online", "payment.cash.description": "Pay with cash upon delivery or pickup", "payment.online.description": "Pay online using credit/debit card", "selected.payment.method": "Selected Payment Method", "payment.will.be.collected.on.delivery": "Payment will be collected on delivery", "payment.will.be.processed.online": "Payment will be processed online", "alfursan.membership": "Alfursan Membership", "alfursan": "Alfursan", "use.alfursan.points.for.discount": "Use Alfursan points for discount", "enter.alfursan.member.id": "Enter Alfursan Member ID", "alfursan.member.id": "Alfursan Member ID", "enter.member.id": "Enter member ID", "please.enter.member.id": "Please enter member ID", "fursan.verification.success": "Fursan verification successful", "fursan.verification.failed": "Fursan verification failed", "fursan.verification.error": "Error verifying Fursan membership", "verifying": "Verifying", "verified": "Verified", "verify": "Verify", "alfursan.member.verified": "Alfursan Member Verified", "member.id": "Member ID", "loyalty.points.will.be.applied.automatically": "Loyalty points will be applied automatically", "alfursan.benefits": "Alfursan Benefits", "earn.points.on.every.booking": "Earn points on every booking", "redeem.points.for.discounts": "Redeem points for discounts", "exclusive.member.offers": "Exclusive member offers", "coupon.code": "Coupon Code", "enter.coupon.code.for.discount": "Enter coupon code for discount", "enter.coupon.code": "Enter coupon code", "please.enter.coupon.code": "Please enter coupon code", "please.select.car.first": "Please select a car first", "coupon.applied.successfully": "Coupon applied successfully", "coupon.validation.failed": "Coupon validation failed", "coupon.validation.error": "Error validating coupon", "validating": "Validating", "applied": "Applied", "select.car.to.apply.coupon": "Select a car to apply coupon", "coupon.applied": "Coupon Applied", "coupon.tips": "Coupon Tips", "coupon.tip.valid.with.car": "Coupon must be valid with selected car", "coupon.tip.one.per.booking": "Only one coupon per booking", "coupon.tip.check.expiry": "Check coupon expiry date", "rent.to.own.plans": "Rent-to-Own Plans", "select.car.first.to.see.plans": "Select a car first to see available plans", "no.rent.to.own.plans.available.for.this.car": "No rent-to-own plans available for this car", "choose.rent.to.own.plan.description": "Choose a rent-to-own plan that fits your budget", "popular": "Popular", "total.amount": "Total Amount", "please.select.a.plan": "Please select a plan", "selected.plan.summary": "Selected Plan Summary", "plan.duration": "Plan Duration", "first.payment": "First Payment", "monthly.payment": "Monthly Payment", "final.payment": "Final Payment", "rent.to.own.benefits": "Rent-to-Own Benefits", "own.the.car.at.end.of.term": "Own the car at the end of the term", "flexible.payment.options": "Flexible payment options", "maintenance.included": "Maintenance included", "comprehensive.insurance.included": "Comprehensive insurance included", "calculatingPrice": "Calculating price...", "rental.totalDays": "Total Days", "rental.pricePerDay": "Price per Day", "SAR": "SAR", "bookings.details.title": "Booking Details", "bookings.details.bookingNumber": "Booking Number", "bookings.details.customerInformation": "Customer Information", "bookings.details.customerId": "Customer ID", "bookings.details.customerRating": "Customer Rating", "bookings.details.assignment": "Assignment", "bookings.details.paymentMethod": "Payment Method", "bookings.details.subStatus": "Sub Status", "bookings.details.dropOffDate": "Drop-off Date", "bookings.details.dropOffTime": "Drop-off Time", "bookings.details.pickUpDate": "Pick-up Date", "bookings.details.pickUpTime": "Pick-up Time", "bookings.details.deliveryType": "Delivery Type", "bookings.details.customerLat": "Customer Latitude", "bookings.details.customerLng": "Customer Longitude", "bookings.details.unlimitedMileage": "Unlimited Mileage", "bookings.details.withWallet": "With Wallet", "bookings.details.withInstallment": "With Installment", "bookings.details.branchLat": "Branch Latitude", "bookings.details.branchLng": "Branch Longitude", "bookings.details.branchName": "Branch Name", "bookings.details.branchNameAr": "Branch Name (Arabic)", "bookings.details.pickUpCity": "Pick-up City", "bookings.details.pickUpCityEn": "Pick-up <PERSON> (English)", "bookings.details.dropOffCity": "Drop-off City", "bookings.details.dropOffCityEn": "Drop-off City (English)", "bookings.details.insurance": "Insurance", "bookings.details.insuranceEn": "Insurance (English)", "bookings.details.dropOffBranch": "Drop-off Branch", "bookings.details.dropOffBranchEn": "Drop-off Branch (English)", "bookings.details.vehicleName": "Vehicle Name", "bookings.details.vehicleNameEn": "Vehicle Name (English)", "bookings.actions.timeline": "Timeline", "bookings.actions.changeStatus": "Change Status", "bookings.actions.extend": "Extend", "bookings.actions.addNote": "Add Note", "bookings.actions.updatePrice": "Update Price", "bookings.actions.changeDuration": "Change Duration", "bookings.actions.updateServices": "Update Services", "bookings.actions.print": "Print", "bookings.notes.addNote.title": "Add Note", "bookings.notes.addNote.noteType": "Note Type", "bookings.notes.addNote.visibility": "Visibility", "bookings.notes.addNote.content": "Note Content", "bookings.notes.addNote.publicNote": "Public Note", "bookings.notes.addNote.publicDescription": "Visible to customers and staff", "bookings.notes.addNote.internalNote": "Internal Note", "bookings.notes.addNote.internalDescription": "Only visible to staff members", "bookings.notes.addNote.minCharacters": "Minimum 10 characters required", "bookings.notes.addNote.placeholder": "Enter your note here...", "bookings.notes.addNote.submit": "Add Note", "bookings.notes.types.general": "General", "bookings.notes.types.customerService": "Customer Service", "bookings.notes.types.technical": "Technical", "bookings.notes.types.payment": "Payment", "bookings.notes.types.complaint": "<PERSON><PERSON><PERSON><PERSON>", "bookings.notes.types.followUp": "Follow Up", "bookings.notes.types.resolution": "Resolution", "bookings.notes.validation.contentRequired": "Note content is required", "bookings.notes.validation.minLength": "Note must be at least 10 characters long", "bookings.notes.validation.maxLength": "Note cannot exceed 1000 characters", "bookings.notes.errors.failedToAdd": "Failed to add note", "bookings.notes.errors.generalError": "An error occurred while adding the note", "bookings.notes.errors.noteAddedSuccessfully": "Note added successfully", "bookings.notes.recentNotes": "Recent Notes", "bookings.notes.createdBy": "Created by", "bookings.notes.adding": "Adding...", "bookings.status.note": "Note", "bookings.status.notePlaceholder": "Write your note here...", "bookings.status.changeTitle": "Change Booking Status", "bookings.status.change": "Change", "bookings.status.currentStatus": "Current Status", "bookings.status.newStatus": "New Status", "bookings.status.reason": "Reason", "bookings.status.reasonPlaceholder": "Please provide a reason for this status change...", "bookings.status.summary": "Summary", "bookings.status.destructiveWarning": "This action cannot be undone. Please confirm you want to proceed.", "bookings.status.changeStatus": "Change Status", "bookings.status.pending": "Pending", "bookings.status.confirmed": "Confirmed", "bookings.status.carReceived": "Car Received", "bookings.status.invoiced": "Invoiced", "bookings.status.closed": "Closed", "bookings.status.cancelled": "Cancelled", "bookings.status.newRequest": "New Request", "bookings.status.errors.selectNewStatus": "Please select a new status", "bookings.status.errors.reasonRequired": "Reason is required", "bookings.extend.title": "Extend Rental Period", "bookings.extend.currentRentalInfo": "Current Rental Information", "bookings.extend.currentEndDate": "Current End Date", "bookings.extend.dailyRate": "Daily Rate", "bookings.extend.quickOptions": "Quick Extension Options", "bookings.extend.daysOption": "{{days}} days", "bookings.extend.newEndDate": "New End Date", "bookings.extend.summary": "Extension Summary", "bookings.extend.additionalDays": "Additional Days", "bookings.extend.additionalCost": "Additional Cost", "bookings.extend.reason": "Reason for Extension", "bookings.extend.reasonPlaceholder": "Please provide a reason for extending the rental...", "bookings.extend.maxExtensionWarning": "Maximum extension allowed is {{days}} days", "bookings.extend.submitting": "Processing...", "bookings.extend.submit": "Extend Rental", "bookings.extend.maxExtension": "Maximum extension", "bookings.extend.currentEndTime": "Current End Time", "bookings.extend.newEndTime": "New End Time", "bookings.extend.createRequest": "Create Request", "bookings.extend.createRequestSuccess": "Request created successfully", "bookings.extend.createRequestError": "Failed to create request", "bookings.extend.createRequestWarning": "Are you sure you want to create a request to extend the rental period?", "bookings.extend.paymentMethod": "Payment Method", "bookings.extend.paymentMethodPlaceholder": "Select a payment method", "bookings.extend.paymentMethodError": "Payment method is required", "bookings.extend.pricePerDay": "Price per day", "bookings.extend.noInstallment": "No Installment", "bookings.extend.withInstallment": "With Installment", "bookings.extend.newEndDateTime": "New End Date and Time", "Statistics": "Statistics", "Bookings": "Bookings", "sidebar.businessBookings": "Business Bookings", "businessBookings.details.title": "Business Booking Details", "businessBookings.details.bookingNumber": "Booking Number", "businessBookings.details.customerInformation": "Customer Information", "businessBookings.details.customerId": "Customer ID", "businessBookings.details.businessDetails": "Business Details", "businessBookings.details.businessActivity": "Business Activity", "businessBookings.details.carDetails": "Car Details", "businessBookings.details.numberOfCars": "Number of Cars", "businessBookings.details.numberOfMonths": "Number of Months", "businessBookings.details.pickupLocation": "Pickup Location", "businessBookings.details.pickupDate": "Pickup Date", "businessBookings.details.dropOffDate": "Drop Off Date", "businessBookings.details.insurance": "Insurance", "businessBookings.details.pricePerMonth": "Price Per Month", "businessBookings.details.totalPrice": "Total Price", "businessBookings.details.acceptedOffer": "Accepted Offer", "businessBookings.details.offerPrice": "Offer Price", "businessBookings.details.allyCompany": "Ally Company", "businessBookings.details.kilometerPerMonth": "Kilometer Per Month", "businessBookings.details.additionalKilometer": "Additional Kilometer Cost", "businessBookings.details.carInsuranceStandard": "Standard Insurance", "businessBookings.details.carInsuranceFull": "Full Insurance", "businessBookings.details.offerDate": "Offer Date", "businessBookings.details.allOffers": "All Offers", "businessBookings.details.offer": "Offer", "businessBookings.details.additionalNotes": "Additional Notes", "businessBookings.actions.timeline": "Timeline", "businessBookings.actions.changeStatus": "Change Status", "businessBookings.actions.viewDetails": "View Details", "businessBookings.timeline.title": "Business Booking Timeline", "businessBookings.changeStatus.title": "Change Business Booking Status", "businessBookings.changeStatus.currentStatus": "Current Status", "businessBookings.changeStatus.newStatus": "New Status", "businessBookings.changeStatus.reason": "Reason", "businessBookings.changeStatus.notes": "Notes", "businessBookings.changeStatus.reasonPlaceholder": "Please provide a reason for this change", "businessBookings.changeStatus.notesPlaceholder": "Optional notes about this status change", "businessBookings.changeStatus.submit": "Change Status", "businessBookings.statusChangedFrom": "Status changed from", "businessBookings.assignmentChangedFrom": "Assignment changed from", "businessBookings.recordUpdated": "Record updated", "businessBookings.noTimelineData": "No timeline data available", "businessBookings.statusChangedSuccess": "Status changed successfully", "businessBookings.statusChangeFailed": "Failed to change status", "businessBookings.selectStatus": "Please select a status", "businessBookings.reasonRequired": "Reason is required for rejection or cancellation", "Unsupported status": "Unsupported status", "businessRental.details": "Business Rental Details", "request.details": "Request Details", "customer.details": "Customer Details", "business.requestId": "Request ID", "business.carNumbers": "Car Numbers", "business.durationMonths": "Duration in months", "business.expectedPickupDate": "Expected pick up date", "business.insuranceType": "Insurance type", "business.additionalNotes": "Additional notes", "business.offerId": "Offer ID", "business.offerPrice": "Offer Price", "business.carInsurance": "Car Insurance", "business.insuranceValue": "Insurance value", "business.monthlyInsuranceValue": "Monthly Insurance value", "business.kilometerAllowed": "Kilometer allowed per month", "business.additionalDistanceCost": "Additional distance cost", "business.offerDate": "Offer date", "business.standard": "Standard", "business.full": "Full", "business.customerId": "Customer ID", "business.nationalId": "National ID", "business.company": "Company", "business.dateOfBirth": "Date of Birth", "business.noCustomerData": "No customer data available", "business.noData": "No data available", "business.changeStatus": "Change Status", "business.offers": "Business Offers", "businessBookings.details.pickupLocationMap": "Pickup Location", "businessBookings.details.mapPlaceholder": "Map integration would show the exact location here", "businessBookings.priceSummary.title": "Price Summary", "businessBookings.priceSummary.basicInfo": "Basic Information", "businessBookings.priceSummary.acceptedOffer": "Accepted Offer", "businessBookings.priceSummary.totalSummary": "Total Summary", "businessBookings.priceSummary.allOffers": "All Offers", "businessBookings.km": "km", "businessBookings.month": "month", "Customers": "Customers", "Allies": "Allies", "Ally Companies": "Ally Companies", "Branches": "Branches", "Managers": "Managers", "Allies Rate": "Allies Rate", "Cars": "Cars", "Listing Cars": "Listing Cars", "Makes": "Makes", "Models": "Models", "Car Versions": "Car Versions", "Features": "Features", "Settings": "Settings", "Banners": "Banners", "Extra Service": "Extra Service", "Coupon": "Coupon", "Packages": "Packages", "Loyalty Partners": "Loyalty Partners", "Roles & Permissions": "Roles & Permissions", "Roles": "Roles", "Users": "Users", "Language": "Language", "Language Selection": "Language Selection", "English": "English", "Arabic": "Arabic", "Accessibility": "Accessibility", "Accessibility Options": "Accessibility Options", "Dark Mode": "Dark Mode", "Light Mode": "Light Mode", "High Contrast": "High Contrast", "Large Text": "Large Text", "Reduced Motion": "Reduced Motion", "Toggle dark mode": "Toggle dark mode", "Toggle high contrast mode for better visibility": "Toggle high contrast mode for better visibility", "Toggle large text mode for better readability": "Toggle large text mode for better readability", "Toggle reduced motion for fewer animations": "Toggle reduced motion for fewer animations", "Dashboard": "Dashboard", "Carwah": "Carwah", "All rights reserved": "All rights reserved", "Logout": "Logout", "Dashboard Statistics": "Dashboard Statistics", "Total Rentals": "Total Rentals", "Active Rentals": "Active Rentals", "Rent-to-Own": "Rent-to-Own", "Total Users": "Total Users", "Rentals Over Time": "Rentals Over Time", "Rentals by Status": "Rentals by Status", "User Growth": "User Growth", "Rentals": "Rentals", "Rent to Own": "Rent to Own", "Count": "Count", "Percentage": "Percentage", "Error loading statistics data. Please try again later.": "Error loading statistics data. Please try again later.", "Create New Booking": "Create New Booking", "All": "All", "Pending": "Pending", "Confirmed": "Confirmed", "Car Received": "Car Received", "Invoiced": "Invoiced", "Closed": "Closed", "Cancelled": "Cancelled", "car_received": "Car Received", "Car received": "Car Received", "pending": "Pending", "confirmed": "Confirmed", "invoiced": "Invoiced", "closed": "Closed", "cancelled": "Cancelled", "Closed after confirm": "Closed After Confirm", "closed_after_confirm": "Closed After Confirm", "Booking extended": "Booking Extended", "booking_extended": "Booking Extended", "Rated": "Rated", "rated": "Rated", "Pickup overdue": "Pickup Overdue", "pickup_overdue": "Pickup Overdue", "Cancelled after confirm": "Cancelled After Confirm", "cancelled_after_confirm": "Cancelled After Confirm", "New": "New", "new": "New", "cities": "Cities", "vehicleType": "vehicle Type", "RENTAL": "Rental ", "RENT_TO_OWN": "Rent To Own", "branches": "branches", "rentType": "Rent Type", "Due invoice": "Due Invoice", "due_invoice": "Due Invoice", "Pending extend": "Pending Extend", "pending_extend": "Pending Extend", "Late Confirmation": "Late Confirmation", "late_confirmation": "Late Confirmation", "Rejected extend": "Rejected Extend", "rejected_extend": "Rejected Extend", "Pending review": "Pending Review", "pending_review": "Pending Review", "Declined": "Declined", "declined": "Declined", "Active Filters": "Active Filters", "Clear All": "Clear All", "Filters": "Filters", "Hide Filters": "Hide Filters", "Show Filters": "Show Filters", "Error loading bookings data. Please try again later.": "Error loading bookings data. Please try again later.", "Error loading business bookings data. Please try again later.": "Error loading business bookings data. Please try again later.", "No business bookings found": "No business bookings found", "Business Bookings": "Business Bookings", "Duration in months": "Duration (Months)", "numberOfCars": "Number of Cars", "cars": "cars", "Total Price": "Total Price", "Ally Company Name": "Ally Company Name", "Enter ally company name": "Enter ally company name", "Enter customer name": "Enter customer name", "Enter customer NID": "Enter customer NID", "customerName.placeholder": "Enter customer name", "customerMobile.placeholder": "Enter customer mobile number", "bookingNo.placeholder": "Enter booking number", "allyCompanyName.placeholder": "Enter ally company name", "Customer NID": "Customer NID", "Booking Status": "Booking Status", "Enter booking number": "Enter booking number", "Enter customer mobile": "Enter customer mobile", "bookings.list.customerName": "Customer Name", "bookings.list.allyName": "Ally Company", "bookings.list.pickupCity": "Pickup City", "bookings.list.pickup": "Pickup Date", "bookings.list.delivery": "Drop-off Date", "bookings.list.header.billingAmountMonth": "Price/Month", "bookings.list.header.billingAmount": "Total Amount", "bookings.list.paidAmount": "<PERSON><PERSON>", "bookings.list.bookingStatus": "Status", "rental.insuranceType": "Insurance Type", "car": "Vehicle", "createdAt": "Created At", "Assign": "Assignment", "Assigned": "Assigned", "Unassigned": "Unassigned", "Search Fields": "Search Fields", "Filter Options": "Filter Options", "Date Filters": "Date Filters", "Apply Filters": "Apply Filters", "Pickup Date From": "Pickup Date From", "Pickup Date To": "Pickup Date To", "Pickup City": "Pickup City", "CONFIRMED": "Confirmed", "CAR_RECEIVED": "Car Received", "INVOICED": "Invoiced", "CANCELLED": "Cancelled", "CLOSED": "Closed", "PENDING": "Pending", "assignment.assignToMe": "Assign to Me", "assignment.reassign": "Reassign", "assignment.pleaseSelectUser": "Please select a user", "assignment.pleaseProvideReason": "Please provide a reason", "assignment.assignmentNotes": "Assignment Notes", "assignment.optionalNotes": "Optional notes about this assignment", "assignment.unassignmentReason": "Unassignment Reason", "assignment.unassignmentReasonPlaceholder": "Please provide a reason for unassigning this booking", "assignment.errorAssigning": "Error assigning booking", "assignment.errorUnassigning": "Error unassigning booking", "assignment.errorLoadingUsers": "Error loading users", "assignment.assignedOn": "Assigned on", "assignment.assigned": "Assigned", "waiting_for_confirmation": "Waiting for Confirmation", "payment_pending": "Payment Pending", "documents_required": "Documents Required", "under_review": "Under Review", "ready_for_pickup": "Ready for Pickup", "delivery_scheduled": "Delivery Scheduled", "contract_signed": "Contract Signed", "insurance_pending": "Insurance Pending", "WAITING_FOR_CONFIRMATION": "Waiting for Confirmation", "PAYMENT_PENDING": "Payment Pending", "DOCUMENTS_REQUIRED": "Documents Required", "UNDER_REVIEW": "Under Review", "READY_FOR_PICKUP": "Ready for Pickup", "DELIVERY_SCHEDULED": "Delivery Scheduled", "CONTRACT_SIGNED": "Contract Signed", "INSURANCE_PENDING": "Insurance Pending", "Search": "Search", "Customer Name": "Customer Name", "Booking Number": "Booking Number", "Plate Number": "Plate Number", "Customer Mobile": "Customer Mobile", "Car Make": "Car Make", "City": "City", "Ally Company": "Ally Company", "Payment Method": "Payment Method", "Payment Brand": "Payment Brand", "Date Range": "Date Range", "Pick-up Date From": "Pick-up Date From", "Pick-up Date": "Pick-up Date", "Drop-off Date From": "Drop-off Date From", "Drop-off Date To": "Drop-off Date To", "Reset": "Reset", "Apply": "Apply", "ID": "ID", "Booking No.": "Booking No.", "Customer": "Customer", "Ally": "Ally", "Car": "Car", "Days": "Days", "Price/Day": "Price/Day", "Total": "Total", "Pick-up": "Pick-up", "Drop-off": "Drop-off", "Status": "Status", "Created At": "Created At", "Actions": "Actions", "Rows per page:": "Rows per page:", "of": "of", "Go to first page": "Go to first page", "Go to last page": "Go to last page", "Go to next page": "Go to next page", "Go to previous page": "Go to previous page", "View Details": "View Details", "Edit": "Edit", "Print": "Print", "Extend": "Extend", "Change Status": "Change Status", "Timeline": "Timeline", "Booking": "Booking", "Extension": "Extension", "Created": "Created", "Per Page": "Per Page", "Pick-up Location": "Pick-up Location", "Drop-off Location": "Drop-off Location", "Payment Details": "Payment Details", "Payment Status": "Payment Status", "Payment Type": "Payment Type", "Payment Date": "Payment Date", "Payment Amount": "Payment Amount", "Payment Reference": "Payment Reference", "Payment Gateway": "Payment Gateway", "Payment Currency": "Payment Currency", "Payment Description": "Payment Description", "Payment Notes": "Payment Notes", "Booking Details": "Booking Details", "Error loading booking details. Please try again later.": "Error loading booking details. Please try again later.", "Refunded": "Refunded", "Payment Information": "Payment Information", "Total Amount": "Total Amount", "Paid Amount": "<PERSON><PERSON>", "Paid": "Paid", "Unpaid": "Unpaid", "Customer Information": "Customer Information", "Customer ID": "Customer ID", "Customer Rating": "Customer Rating", "Vehicle Information": "Vehicle Information", "Car ID": "Car ID", "Daily Price": "Daily Price", "Unlimited Mileage": "Unlimited Mileage", "Rental Period": "Rental Period", "Duration": "Duration", "days": "days", "Ally ID": "Ally ID", "Ally Rating": "<PERSON>", "Delivery Information": "Delivery Information", "Delivery Type": "Delivery Type", "Delivery Address": "Delivery Address", "Delivery Price": "Delivery Price", "Handover Price": "Handover Price", "Price Breakdown": "Price Breakdown", "Number of Days": "Number of Days", "Price Before Tax": "Price Before Tax", "Tax": "Tax", "Insurance": "Insurance", "Delivery": "Delivery", "Discount": "Discount", "Extra Services": "Extra Services", "Price": "Price", "Notes": "Notes", "Plate": "Plate", "Error loading timeline data": "Error loading timeline data", "No timeline events found": "No timeline events found", "Status is required": "Status is required", "Reason is required for cancellation or closure": "Reason is required for cancellation or closure", "Booking status changed successfully": "Booking status changed successfully", "Select Status": "Select Status", "Reason": "Reason", "Optional reason for status change": "Optional reason for status change", "Save Changes": "Save Changes", "Cancel": "Cancel", "Drop-off date is required": "Drop-off date is required", "Drop-off date must be in the future": "Drop-off date must be in the future", "Payment method is required": "Payment method is required", "Booking extended successfully": "Booking extended successfully", "Extend Booking": "Extend Booking", "New Drop-off Date": "New Drop-off Date", "Select new drop-off date and time": "Select new drop-off date and time", "Select Payment Method": "Select Payment Method", "Credit Card": "Credit Card", "Cash": "Cash", "Bank Transfer": "Bank Transfer", "Extension Days": "Extension Days", "Additional Cost": "Additional Cost", "More actions": "More actions", "Error loading booking data": "Error loading booking data", "Submitting...": "Submitting...", "By": "By", "New Status": "New Status", "Current Status": "Current Status", "Current Details": "Current Details", "Current Drop-off Date": "Current Drop-off Date", "Extension Details": "Extension Details", "Extension Summary": "Extension Summary", "Additional Days": "Additional Days", "Estimated Total": "Estimated Total", "Card": "Card", "changeDuration.title": "Change Rental Duration", "changeDuration.currentRentalPeriod": "Current Rental Period", "changeDuration.startDate": "Start Date", "changeDuration.endDate": "End Date", "changeDuration.duration": "Duration", "changeDuration.quickAdjustments": "Quick Adjustments", "changeDuration.add1Day": "Add 1 day", "changeDuration.add3Days": "Add 3 days", "changeDuration.add7Days": "Add 7 days", "changeDuration.remove1Day": "Remove 1 day", "changeDuration.remove3Days": "Remove 3 days", "changeDuration.newStartDate": "New Start Date", "changeDuration.newEndDate": "New End Date", "changeDuration.summary": "Duration Change Summary", "changeDuration.newDuration": "New Duration", "changeDuration.change": "Change", "changeDuration.priceAdjustment": "Price Adjustment", "changeDuration.newTotal": "New Total", "changeDuration.shiftInfo": "You are shifting the rental period without changing the duration. No price adjustment will be applied.", "changeDuration.extensionWarning": "Warning: This is a significant extension ({{days}} days). Please ensure this is intentional.", "changeDuration.updating": "Updating...", "changeDuration.submit": "Change Duration", "bookings.updateServices.title": "Update Extra Services", "bookings.updateServices.branchServices": "Branch Services", "bookings.updateServices.allyServices": "Ally Services", "bookings.updateServices.required": "Required", "bookings.updateServices.noServicesAvailable": "No extra services available for this booking", "bookings.updateServices.totalCost": "Total Extra Services Cost", "bookings.updateServices.cancel": "Cancel", "bookings.updateServices.submit": "Update Services", "bookings.updateServices.updating": "Updating...", "bookings.updatePrice.title": "Update Suggested Price", "bookings.updatePrice.currentPriceInfo": "Current Price Information", "bookings.updatePrice.originalPrice": "Original Price", "bookings.updatePrice.currentSuggestedPrice": "Current Suggested Price", "bookings.updatePrice.priceBreakdown": "Price Breakdown", "bookings.updatePrice.basePrice": "Base Price", "bookings.updatePrice.taxes": "Taxes", "bookings.updatePrice.fees": "Fees", "bookings.updatePrice.insurance": "Insurance", "bookings.updatePrice.extraServices": "Extra Services", "bookings.updatePrice.discounts": "Discounts", "bookings.updatePrice.newSuggestedPrice": "New Suggested Price", "bookings.updatePrice.priceChangeSummary": "Price Change Summary", "bookings.updatePrice.difference": "Difference", "bookings.updatePrice.percentageChange": "Percentage Change", "bookings.updatePrice.newTotal": "New Total", "bookings.updatePrice.reasonForChange": "Reason for Price Change", "bookings.updatePrice.reasonPlaceholder": "Please provide a detailed reason for changing the price...", "bookings.updatePrice.minCharactersRequired": "Minimum 10 characters required", "bookings.updatePrice.priceRequired": "Price is required", "bookings.updatePrice.validPriceRequired": "Please enter a valid price", "bookings.updatePrice.priceMustBeDifferent": "New price must be different from current price", "bookings.updatePrice.reasonRequired": "Reason for price change is required", "bookings.updatePrice.reasonMinLength": "Reason must be at least 10 characters long", "bookings.updatePrice.priceChangeLimit": "Price change cannot exceed 50% of the original price", "bookings.updatePrice.failedToUpdate": "Failed to update price", "bookings.updatePrice.generalError": "An error occurred while updating the price", "bookings.updatePrice.significantChangeWarning": "Warning: This is a significant price change ({{percentage}}%). Please ensure this is intentional.", "bookings.updatePrice.cancel": "Cancel", "bookings.updatePrice.submit": "Update Price", "bookings.updatePrice.updating": "Updating...", "bookings.timeline.title": "Booking Timeline", "bookings.timeline.bookingCreated": "Booking Created", "bookings.timeline.payment": "Payment", "bookings.timeline.assignment": "Assignment", "bookings.timeline.note": "Note", "bookings.timeline.statusChange": "Status Change", "bookings.timeline.durationChange": "Duration Change", "bookings.timeline.priceUpdate": "Price Update", "bookings.timeline.extension": "Extension", "bookings.timeline.refund": "Refund", "bookings.timeline.close": "Close", "bookings.timeline.oldData": "Old Data", "bookings.timeline.newData": "New Data", "bookings.timeline.noDataFound": "No data found", "bookings.timeline.extensionId": "Extension ID", "bookings.timeline.by": "By", "bookings.timeline.details": "Details", "BookingTimeLine": "Booking Timeline", "oldData": "Old Data", "newData": "New Data", "No data found": "No data found", "bookingId.placeholder": "Booking ID", "Extension.id": "Extension ID", "error.loadingTimeline": "Error loading timeline data", "timeline": {"title": "Booking Timeline", "bookingCreated": "Booking Created", "bookingConfirmed": "Booking Confirmed", "bookingCancelled": "Booking Cancelled", "bookingCompleted": "Booking Completed", "paymentReceived": "Payment Received", "paymentFailed": "Payment Failed", "carAssigned": "Car Assigned", "carDelivered": "Car Delivered", "carReturned": "Car Returned", "close": "Close"}, "print": {"bookingDetails": "Booking Details", "bookingInformation": "Booking Information", "bookingNumber": "Booking Number", "status": "Status", "createdAt": "Created At", "duration": "Duration", "days": "days", "customerInformation": "Customer Information", "customerName": "Customer Name", "mobile": "Mobile", "email": "Email", "vehicleInformation": "Vehicle Information", "make": "Make", "model": "Model", "year": "Year", "rentalPeriod": "Rental Period", "pickupDate": "Pick-up Date", "dropoffDate": "Drop-off Date", "branch": "Branch", "totalAmount": "Total Amount", "printedOn": "Printed on", "allRightsReserved": "All rights reserved", "unableToOpenPrintWindow": "Unable to open print window. Please check your popup blocker."}, "map": {"location": "Location", "directions": "Get Directions", "openInMaps": "Open in Maps", "copyLocation": "Copy Location", "branchPhone": "Phone", "mapError": "Map loading error", "checkConnection": "Please check your internet connection", "geolocationNotSupported": "Geolocation is not supported by this browser", "directionsError": "Directions error", "locationError": "Location error", "locationCopied": "Location copied to clipboard", "invalidCoordinates": "Invalid coordinates provided"}, "assignment": {"title": "Assignment", "noAssignmentFound": "No assignment found", "notAssignedMessage": "This booking is not assigned to any customer care representative", "assignNow": "Assign Now", "currentlyAssignedTo": "Currently assigned to", "selectUser": "Select User", "assignBooking": "Assign Booking", "unassignBooking": "Unassign Booking", "activeAssignments": "active assignments", "assignmentNotes": "Assignment Notes", "optionalNotes": "Optional notes about this assignment...", "unassignmentReason": "Unassignment Reason", "unassignmentReasonPlaceholder": "Please provide a reason for unassigning this booking...", "assign": "Assign", "unassign": "Unassign", "assignmentHistory": "Assignment History", "viewAssignmentHistory": "View Assignment History", "reassignOrUnassign": "Reassign or Unassign", "assigned": "Assigned", "assignedOn": "Assigned on", "assignedBy": "Assigned by", "unassignedBy": "Unassigned by", "active": "Active", "errorLoadingHistory": "Error loading assignment history", "noHistoryFound": "No assignment history found", "pleaseSelectUser": "Please select a user", "pleaseProvideReason": "Please provide a reason for unassignment", "errorAssigning": "Failed to assign booking. Please try again.", "errorUnassigning": "Failed to unassign booking. Please try again.", "errorLoadingUsers": "Error loading customer care users"}, "bookings": {"title": "Bookings", "details": {"title": "Booking Details", "bookingNumber": "Booking Number", "customerInformation": "Customer Information", "customerId": "Customer ID", "customerRating": "Customer Rating", "assignment": "Assignment"}, "actions": {"timeline": "Timeline", "changeStatus": "Change Status", "extend": "Extend", "addNote": "Add Note", "updatePrice": "Update Price", "changeDuration": "Change Duration", "updateServices": "Update Services", "print": "Print"}, "notes": {"add": "Add", "addNote": {"title": "Add Note", "content": "Note Content", "placeholder": "Enter your note here..."}, "validation": {"contentRequired": "Note content is required"}, "errors": {"failedToAdd": "Failed to add note", "generalError": "An error occurred while adding the note", "noteAddedSuccessfully": "Note added successfully"}, "createdBy": "Created by"}}, "bookings.installments.title": "Rental Installments", "bookings.installments.installmentNumber": "Installment", "bookings.installments.amount": "Amount", "bookings.installments.dueDate": "Due Date", "bookings.installments.status": "Installment Status", "bookings.installments.paymentMethod": "Payment Method", "bookings.installments.actions": "Actions", "bookings.installments.refund": "Refund", "bookings.installments.changeStatus": "Change Status", "bookings.installments.refundAmount": "Refund Amount", "bookings.installments.maximumRefundable": "Maximum Refundable Amount", "bookings.installments.refundInstallment": "Refund Installment", "bookings.installments.upcoming": "Upcoming", "bookings.installments.paid": "Paid", "bookings.installments.due": "Due", "bookings.installments.overdue": "Overdue", "bookings.installments.notCollected": "Not Collected", "bookings.installments.notCollectable": "Not Collectable", "bookings.installments.fullyRefunded": "Fully Refunded", "bookings.installments.partiallyRefunded": "Partially Refunded", "bookings.installments.paidAt": "<PERSON><PERSON>", "bookings.installments.paymentBrand": "Payment Brand", "bookings.installments.walletPaid": "<PERSON><PERSON>", "bookings.installments.loyaltyPoints": "Loyalty Points", "bookings.installments.pendingTransaction": "Pending Transaction", "bookings.installments.canSendToAlly": "Can Send to Ally", "bookings.installments.totalRemainingPrice": "Total Remaining Price", "bookings.installments.noInstallmentsFound": "No installments found", "bookings.installments.earnStatus": "Earn Status", "bookings.installments.milesPrice": "<PERSON>", "bookings.installments.editStatus": "Edit Status", "bookings.installments.recallPaymentGateway": "Recall Payment Gateway", "bookings.installments.amountRequired": "Amount is required", "bookings.installments.amountExceedsInstallment": "Amount cannot exceed installment amount", "bookings.extensionRequests.title": "Extension Requests", "bookings.extensionRequests.requestNumber": "Request No", "bookings.extensionRequests.requestedDays": "Requested Days", "bookings.extensionRequests.approvedDays": "Approved Days", "bookings.extensionRequests.status": "Extension Status", "bookings.extensionRequests.requestDate": "Request Date", "bookings.extensionRequests.responseDate": "Response Date", "bookings.extensionRequests.additionalCost": "Additional Cost", "bookings.extensionRequests.actions": "Actions", "bookings.extensionRequests.approve": "Approve", "bookings.extensionRequests.reject": "Reject", "bookings.extensionRequests.view": "View", "bookings.extensionRequests.pending": "Pending", "bookings.extensionRequests.approved": "Approved", "bookings.extensionRequests.rejected": "Rejected", "bookings.extensionRequests.confirmed": "Confirmed", "bookings.extensionRequests.paid": "Paid", "bookings.extensionRequests.approveRequest": "Approve Extension Request", "bookings.extensionRequests.rejectRequest": "Reject Extension Request", "bookings.extensionRequests.viewDetails": "Extension Request Details", "bookings.extensionRequests.requestedBy": "Requested By", "bookings.extensionRequests.originalEndDate": "Original End Date", "bookings.extensionRequests.newEndDate": "New End Date", "bookings.extensionRequests.rejectionReason": "Rejection Reason", "bookings.extensionRequests.approvalNotes": "Approval Notes", "bookings.extensionRequests.paymentMethod": "Payment Method", "bookings.extensionRequests.totalRemainingPrice": "Total Remaining Price", "bookings.extensionRequests.noExtensionRequestsFound": "No extension requests found", "bookings.extensionRequests.approvedDaysRequired": "Approved days is required", "bookings.extensionRequests.rejectionReasonRequired": "Rejection reason is required", "bookings.extensionRequests.approvedBy": "Approved By", "bookings.extensionRequests.addRequest": "Add Request", "bookings.extensionRequests.oneMonth": "One Month", "bookings.extensionRequests.twoMonths": "Two Months", "bookings.extensionRequests.threeMonths": "Three Months", "bookings.extensionRequests.fourMonths": "Four Months", "bookings.extensionRequests.fiveMonths": "Five Months", "bookings.extensionRequests.sixMonths": "Six Months", "bookings.extensionRequests.sevenMonths": "Seven Months", "bookings.extensionRequests.eightMonths": "Eight Months", "bookings.extensionRequests.nineMonths": "Nine Months", "bookings.extensionRequests.tenMonths": "Ten Months", "bookings.extensionRequests.elevenMonths": "Eleven Months", "bookings.extensionRequests.twelveMonths": "Twelve Months", "bookings.extensionRequests.expired": "Expired", "bookings.extensionRequests.cancelled": "Cancelled", "bookings.extensionRequests.completed": "Completed", "bookings.extensionRequests.total": "Total", "bookings.installments.resendToAlly": "Resend to Ally", "bookings.installments.refundConfirmation": "Are you sure you want to refund this installment?", "bookings.installments.fullRefund": "Full Refund", "bookings.installments.partialRefund": "Partial Refund", "bookings.installments.bankTransferRefund": "Bank Transfer Refund", "bookings.installments.online": "Online", "bookings.installments.walletValue": "Wallet Value", "bookings.installments.earningValue": "Earning Value", "bookings.installments.cash": "Cash", "bookings.payment.notPaid": "Not Paid", "bookings.payment.online": "Online", "bookings.payment.cash": "Cash", "bookings.payment.wallet": "Wallet", "bookings.delivery.noDelivery": "No Delivery", "bookings.delivery.delivery": "Delivery", "assignment.title": "Assignment", "assignment.noAssignmentFound": "No assignment found", "delivery_type": "Delivery Type", "no_delivery": "No Delivery", "delivery": "Delivery", "payment_method": "Payment Method", "drop_off_date": "Drop-off Date", "vehicle_name": "Vehicle Name", "customer_information": "Customer Information", "booking_number": "Booking Number", "bookings.details.allyCompany": "Ally Company", "bookings.details.allyId": "Ally ID", "bookings.details.allyRating": "<PERSON>", "bookings.details.vehicleInformation": "Vehicle Information", "bookings.details.carId": "Car ID", "bookings.details.dailyPrice": "Daily Price", "bookings.details.rentalPeriod": "Rental Period", "bookings.details.pickup": "Pick-up", "bookings.details.dropoff": "Drop-off", "bookings.details.duration": "Duration", "bookings.details.days": "days", "bookings.details.paymentInformation": "Payment Information", "bookings.details.totalAmount": "Total Amount", "bookings.details.paidAmount": "<PERSON><PERSON>", "bookings.details.paymentStatus": "Payment Status", "bookings.details.paid": "Paid", "bookings.details.unpaid": "Unpaid", "bookings.details.created": "Created", "bookings.details.invoiced": "Invoiced", "bookings.details.refunded": "Refunded", "bookings.details.priceBreakdown": "Price Breakdown", "bookings.details.numberOfDays": "Number of Days", "bookings.details.priceBeforeTax": "Price Before Tax", "bookings.details.tax": "Tax", "bookings.details.delivery": "Delivery", "bookings.details.discount": "Discount", "bookings.details.extraServices": "Extra Services", "bookings.details.price": "Price", "bookings.details.notes": "Notes", "bookings.details.deliveryInformation": "Delivery Information", "bookings.details.deliveryAddress": "Delivery Address", "bookings.details.deliveryPrice": "Delivery Price", "bookings.details.handoverPrice": "Handover Price", "bookings.extensionRequests.newReturnTime": "New Return Time", "bookings.extensionRequests.extensionDays": "Extension Days", "bookings.extensionRequests.dueValue": "Due Value", "bookings.extensionRequests.originalPricePerDay": "Original Price/Day", "bookings.extensionRequests.pricePerDay": "Price/Day", "bookings.extensionRequests.suggestedPricePerDay": "Suggested Price/Day", "bookings.extensionRequests.discountRate": "Discount Rate", "bookings.extensionRequests.paymentBrand": "Payment Brand", "bookings.extensionRequests.requestStatus": "Request Status", "bookings.extensionRequests.paymentStatus": "Payment Status", "common.noNotesAvailable": "No notes available", "addBooking": "Add Booking", "EditBooking": "Edit Booking", "bookingType": "Booking Type", "daily": "Daily", "monthly": "Monthly", "rentToOwn": "Rent to Own", "withInstallment": "With Installment", "Installments.booking": "Installment Booking", "rental.customer.details": "Customer Details", "customerName": "Customer Name", "customerMobile": "Customer Mobile", "customerEmail": "Customer <PERSON><PERSON>", "rental.bookingTiming": "Booking Timing", "pickUpDate": "Pick-up Date", "dropOffDate": "Drop-off Date", "rental.pickupDateTime": "Pick-up Date & Time", "rental.dropoffDateTime": "Drop-off Date & Time", "rental.dropoffTime": "Drop-off Time", "months.count": "Number of Months", "1 month": "1 Month", "3 months": "3 Months", "6 months": "6 Months", "12 months": "12 Months", "rental.bookingLocation": "Booking Location", "same.as.pickup.location": "Same as pickup location", "samePickupAndReturn": "Same pickup and return location", "rental.pickupLocation": "Pickup Location", "dropoff-location": "Drop-off Location", "thisfieldisrequired": "This field is required", "mapComponentPlaceholder": "Map Component (To be implemented)", "rental.rentalCar": "Rental Car", "selecting.company": "Select Company", "selecting.branch": "Select Branch", "select.Pickup branch": "Select Pickup Branch", "select car": "Select Car", "select.Dropoff branch": "Select Drop-off Branch", "rental.plans": "Rental Plans", "month": "Month", "1st Installment-booking": "1st Installment", "final Installment": "Final Installment", "rental.extraServices": "Extra Services", "Unlimited.KM": "Unlimited KM", "day": "Day", "distanceBetweenCarUser": "Distance from Car to User", "km": "KM", "deliveryPrice": "Delivery Price", "Return to the same delivery location": "Return to Same Delivery Location", "handoverPrice": "Handover Price", "Discount coupon": "Discount Coupon", "couponCode": "Coupon Code", "apply": "Apply", "delete": "Delete", "insurance": "Insurance", "priceCalculationPlaceholder": "Price calculation will be displayed here", "paymentMethod": "Payment Method", "cash": "Cash", "online": "Online", "card": "Card", "bankTransfer": "Bank Transfer", "Alfursan": "Alfursan Loyalty", "suggestedPricePerDay.label": "Suggested Price Per Day", "note": "Notes", "common.saving": "Saving...", "button.save": "Save", "Rent": "Create Booking", "customerDetailsWillBeDisplayedHere": "Customer details will be displayed here", "basicinformation": "Basic Information", "rental.enterphone": "Enter Customer Phone Number", "bookingFormWillBeImplementedNext": "Booking form implementation in progress", "success.create.rental": "Booking created successfully", "success.edit.rental": "Booking updated successfully", "error.submitting.booking": "Error submitting booking", "Invalid coupon": "Invalid coupon code", "pleaseSelectSuitablePicupDate": "Please select a suitable pickup date", "validation.fromMustBeLessThanTo": "Pickup date must be before drop-off date", "handover_branch_price": "Handover Branch Price", "copylink": "Copy Location Link", "Link Copied": "Location link copied to clipboard", "Ally Declined": "This ally has previously declined this booking", "You've chosen an ally who has previously declined this booking": "You've chosen an ally who has previously declined this booking", "Would you like to proceed?": "Would you like to proceed?", "cancel": "Cancel", "Confirm": "Confirm", "Editing this booking period will close the pending extensions": "Editing this booking period will close the pending extensions", "Done successfully": "Operation completed successfully", " Error ": "An error occurred", "Rental Installments": "Rental Installments", "searching": "Searching...", "search": "Search", "rental.bookingType": "Booking Type", "totalPrice": "Total Price", "price.sr/day": "{{price}} SR/day", "use.suggested.price": "Use Suggested Price", "suggested.price.per.day": "Suggested Price Per Day", "enter.custom.price.per.day": "Enter custom price per day", "suggested": "Suggested", "enter.notes.here": "Enter notes here...", "optional.notes.about.booking": "Optional notes about this booking", "select.car.first.to.see.prices": "Select a car first to see prices", "original": "Original", "coupon.discount": "Coupon Discount", "fullPhoneNumber": "Full Phone Number", "countryCode": "Country Code", "Standard": "Standard", "Full": "Full", "booking.summary": "Booking Summary", "review.booking.details.before.confirming": "Please review all booking details before confirming", "full.name": "Full Name", "phone.number": "Phone Number", "email": "Email", "national.id": "National ID", "no.customer.selected": "No customer selected", "pickup.time": "Pickup Time", "return.time": "Return Time", "duration": "Duration", "months": "Months", "company.branch.information": "Company & Branch Information", "company": "Company", "pickup.branch": "Pickup Branch", "dropoff.branch": "Drop-off Branch", "vehicle.information": "Vehicle Information", "car.model": "Car Model", "no.car.selected": "No car selected", "insurance.services": "Insurance & Services", "no.insurance.selected": "No insurance selected", "no.extra.services.selected": "No extra services selected", "delivery.information": "Delivery Information", "delivery.type": "Delivery Type", "one-way.delivery": "One-way Delivery", "two-way.delivery": "Two-way Delivery", "delivery.location": "Delivery Location", "rent.to.own.plan": "Rent to Own Plan", "first.installment": "First Installment", "monthly.installment": "Monthly Installment", "final.installment": "Final Installment", "payment.information": "Payment Information", "fursan.member.id": "Fursan Member ID", "applied.coupon": "Applied Coupon", "discount": "Discount", "price.breakdown": "Price Breakdown", "base.price": "Base Price", "insurance.cost": "Insurance Cost", "important.notes": "Important Notes", "please.review.all.details.carefully": "Please review all details carefully", "booking.confirmation.will.be.sent.via.sms.email": "Booking confirmation will be sent via SMS/Email", "cancellation.policy.applies": "Cancellation policy applies", "terms.and.conditions.apply": "Terms and conditions apply", "CASH": "Cash", "ONLINE": "Online", "CARD": "Card", "rent-to-own": "Rent-to-Own"}