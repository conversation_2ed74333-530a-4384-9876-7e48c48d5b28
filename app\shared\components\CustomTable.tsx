'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Box,
} from '@mui/material';

interface TableColumn {
  headerId: string;
  dataRef: string;
  dataType: string;
}

interface CustomTableProps {
  tableData: TableColumn[];
  tableRecords: any[];
  title?: string;
}

export default function CustomTable({ 
  tableData, 
  tableRecords, 
  title = 'Offers' 
}: CustomTableProps) {
  const { t } = useTranslation();

  const formatCellValue = (value: any, dataType: string) => {
    if (value === null || value === undefined) return '-';
    
    switch (dataType) {
      case 'CURRENCY':
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'SAR',
        }).format(value);
      case 'DATE':
        return new Date(value).toLocaleDateString();
      case 'STATUS':
        return (
          <Chip 
            label={t(value)} 
            size="small" 
            color={getStatusColor(value)}
          />
        );
      default:
        return value;
    }
  };

  const getStatusColor = (status: string): any => {
    switch (status?.toLowerCase()) {
      case 'accepted':
      case 'confirmed':
        return 'success';
      case 'rejected':
      case 'cancelled':
        return 'error';
      case 'pending':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (!tableRecords || tableRecords.length === 0) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            {t(title)}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('No data available')}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          {t(title)} ({tableRecords.length})
        </Typography>
        
        <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
          <Table stickyHeader>
            <TableHead>
              <TableRow>
                {tableData.map((column, index) => (
                  <TableCell 
                    key={index}
                    sx={{ 
                      fontWeight: 600,
                      backgroundColor: 'grey.50',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {t(column.headerId)}
                  </TableCell>
                ))}
              </TableRow>
            </TableHead>
            <TableBody>
              {tableRecords.map((record, rowIndex) => (
                <TableRow 
                  key={rowIndex}
                  sx={{ '&:nth-of-type(odd)': { backgroundColor: 'grey.25' } }}
                >
                  {tableData.map((column, colIndex) => (
                    <TableCell 
                      key={colIndex}
                      sx={{ whiteSpace: 'nowrap' }}
                    >
                      {column.dataRef === 'status' ? (
                        <Chip 
                          label={t(record[column.dataRef])} 
                          size="small" 
                          color={getStatusColor(record[column.dataRef])}
                        />
                      ) : (
                        formatCellValue(record[column.dataRef], column.dataType)
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );
}
