'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent, Typography, Box, Avatar, Stack, Divider } from '@mui/material';
import {
  Phone as PhoneIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';

interface CustomerDataDisplayProps {
  customerDetailsRes: any;
  withimages?: boolean;
}

export default function CustomerDataDisplay({
  customerDetailsRes,
  withimages = true,
}: CustomerDataDisplayProps) {
  const { t } = useTranslation();

  const customer = customerDetailsRes?.user;

  if (!customer) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            {t('customer.details')}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {t('business.noCustomerData')}
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ mb: 3 }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          {t('customer.details')}
        </Typography>
        <Divider sx={{ mb: 2 }} />

        {/* Customer Avatar and Basic Info */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
          {withimages && (
            <Avatar src={customer.profileImage} sx={{ width: 60, height: 60 }}>
              {customer.name?.charAt(0) || customer.customerProfile?.firstName?.charAt(0)}
            </Avatar>
          )}
          <Box>
            <Typography variant="h6">
              {customer.name ||
                `${customer.customerProfile?.firstName || ''} ${
                  customer.customerProfile?.lastName || ''
                }`.trim()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('business.customerId')}: {customer.id}
            </Typography>
          </Box>
        </Box>

        {/* Customer Details */}
        <Stack spacing={2}>
          {customer.mobile && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PhoneIcon fontSize="small" color="action" />
              <Typography variant="body2">{customer.mobile}</Typography>
            </Box>
          )}

          {customer.email && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <EmailIcon fontSize="small" color="action" />
              <Typography variant="body2">{customer.email}</Typography>
            </Box>
          )}

          {customer.customerProfile?.nid && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PersonIcon fontSize="small" color="action" />
              <Typography variant="body2">
                {t('business.nationalId')}: {customer.customerProfile.nid}
              </Typography>
            </Box>
          )}

          {customer.customerProfile?.companyName && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <BusinessIcon fontSize="small" color="action" />
              <Typography variant="body2">
                {t('business.company')}: {customer.customerProfile.companyName}
              </Typography>
            </Box>
          )}

          {customer.customerProfile?.city && (
            <Typography variant="body2">
              {t('City')}: {customer.customerProfile.city.name}
            </Typography>
          )}

          {customer.customerProfile?.dob && (
            <Typography variant="body2">
              {t('business.dateOfBirth')}:{' '}
              {new Date(customer.customerProfile.dob).toLocaleDateString()}
            </Typography>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
}
