'use client';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Grid,
  Divider,
} from '@mui/material';

interface InfoCardProps {
  data: Array<{ msgId: string; value: any }>;
  titleId: string;
  fullwidth?: boolean;
}

export default function InfoCard({ data, titleId, fullwidth = false }: InfoCardProps) {
  const { t } = useTranslation();

  return (
    <Card sx={{ mb: 3, width: fullwidth ? '100%' : 'auto' }}>
      <CardContent>
        <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
          {t(titleId)}
        </Typography>
        <Divider sx={{ mb: 2 }} />
        <Grid container spacing={2}>
          {data.map((item, index) => (
            <Grid item xs={12} sm={6} key={index}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                  {t(item.msgId)}
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {item.value || '-'}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
      </CardContent>
    </Card>
  );
}
